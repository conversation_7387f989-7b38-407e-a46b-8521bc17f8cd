我已经为你们学习了所有关于AI的东西。所以，以下是我认为你在2025年需要了解的关于AI的所有内容的精华版。我们将从初级到中级再到高级，我将为每个主题提供速成课程，并为你提供更多资源，以便你深入研究任何一个主题。视频结束时，你对AI的了解将超过99%的人。但前提是，你得真正记住这些信息。


这是视频的结构。首先，我们将介绍AI的基本定义及其工作原理。然后，我们将讨论提示词工程（prompting），接着是非常热门的AI智能体（agents），然后是AI辅助编码。我们将通过所谓的“氛围感编码（vibe coding）”来构建应用程序，最后展望进入2025年下半年的一些新兴技术。

好的，让我们从定义人工智能开始。人工智能指的是能够完成通常与人类智能相关的认知任务的计算机程序。现在，AI作为一个领域已经存在了很长时间。传统人工智能（我们过去称之为机器学习）的一些例子包括谷歌的搜索算法或YouTube为你推荐像本视频这样内容的内容推荐系统。但我们如今通常所说的AI，被称为生成式AI（Generative AI），它是人工智能的一个特定子集，可以生成新的内容，如文本、图像、音频、视频和其他类型的媒体。

生成式AI模型最受欢迎的例子是能够处理文本并输出文本的模型，也称为大语言模型（LLM）。大语言模型的例子包括OpenAI的GPT系列、谷歌的Gemini以及Anthropic的Claude模型。如今，模型种类繁多，许多模型也原生支持多模态（multimodal），这意味着你不仅可以输入和输出文本，还可以输入输出图像、音频和视频。你最喜欢的模型，如GPT-4o或Gemini 2.5 Pro，都是多模态的。

好的，太棒了。现在你了解了AI世界中使用的一些基本关键术语。现在我会在屏幕上放出这一部分的一个小测验。请在下面的评论中写下你对这些问题的答案。另外，如果你想了解更多关于这些生成式AI模型的细节，包括深入了解这些模型的内部工作原理、它们在工作场所的应用方式以及如何负责任地使用AI，我建议你观看这个视频，我会把链接放在这里，我将谷歌的8小时AI基础课程浓缩成了15分钟。但现在，让我们进入下一部分，关于如何通过提示词工程（prompting）从这些AI模型中获得最大收益。

首先我们来定义提示词工程（prompting）。提示词工程是向生成式AI工具提供具体指令以接收新信息或在某项任务上实现预期结果的过程。这可以通过文本、图像、音频、视频甚至代码来完成。提示词工程是你能学到的投资回报率最高的技能，没有之一。它也是所有其他更高级AI技能的基础。这很有道理，因为提示词是与这些AI模型沟通的方式。就像你可以拥有最高级的模型、最炫的工具、最棒的一切，但如果你不知道如何与它互动，它仍然是无用的。

所以，如果你想作为初学者开始练习提示词，第一步就是选择你最喜欢的AI聊天机器人。可以是ChatGPT、Gemini、Claude或者任何你喜欢的。接下来，我有两个助记法要告诉你，如果你能记住并实施它们，你在提示词方面的能力将超过98%的人。

第一个我称之为“小螃蟹骑巨鬣蜥（tiny crabs ride enormous iguanas）”框架，它代表：任务（Task）、背景（Context）、参考（Resources）、评估（Evaluate）和迭代（Iterate）。当你构建一个提示词时，首先要考虑的是你希望它执行的任务。你希望AI做什么？例如，也许你希望AI帮你写几条Instagram帖子，来推广你的新章鱼周边产品线。你可以直接提示它：“创建一条IG帖子，推广我的新章鱼周边产品”。这样你可能会得到一些还算可以的结果，但你可以让结果变得更好。首先，你可以加入一个人设（persona），告诉AI扮演一位专业的IG网红来写这条帖子。这能让AI扮演IG网红的角色，并利用一些更具体的领域知识来制作更好的IG帖子。然后，你还可以添加期望的输出格式。现在的默认格式是一段普通的文案加一些标签，对吧？但也许你想要一些更结构化的东西。你可以要求它以一个关于章鱼的趣闻开头，然后是发布公告，最后以三个相关的标签结尾。太棒了，现在这看起来已经好多了，但我们还能做得更多。

这个框架的下一部分是背景（context）。一般的经验法则是，你给AI提供的背景信息越多，结果就会越具体、越好。我们现在能提供的最明显的背景信息就是我们正在销售的周边产品的实际图片。我们还可以添加一些关于我们公司的背景，比如我们的公司叫“孤独的章鱼（Lonely Octopus）”，我们教人们AI技能，就像我们最近的AI智能体训练营一样，顺便说一句，上次训练营的名额在40小时内就通过候补名单售罄了。所以非常感谢大家。我们很快会开放新的一期，如果你感兴趣，请务必注册候补名单，我会把链接放在这里，描述里也有链接。总之，我们可以给AI的一些额外背景信息是，我们的吉祥物，也就是周边产品上的这个，叫做Inky。我们还可以更具体地说明我们的发布日期和周边的目标受众，比如20到40岁的人，主要是职场人士之类的。有了这些背景信息，你的结果会更精确，更符合你的要求。

但我们还能做得更好。这就是框架的下一步——参考（references）。在这里，你可以提供一些你喜欢的其他IG帖子的例子。这样AI就可以从中汲取灵感。提供例子非常强大，因为你可以用语言尽可能多地描述，但你知道，如果你直接给它一个例子，里面包含的信息量是巨大的，可以捕捉到那些你想融入结果中的细微差别。瞧，你按下回车，这就是你的IG帖子了。现在，你要进行评估（evaluate）。你喜欢吗？有什么想微调或改变的地方吗？如果有，你就进入框架的最后一步，即迭代（iterate）。与AI模型互动是一个非常迭代的过程。所以即使第一次它没有得到你想要的结果，你也可以告诉它，“这里稍微调整一下”、“在这里加点东西”、“改变某个东西的颜色”，你和AI一起协作，直到得到你最终想要的结果。

“小螃蟹骑巨鬣蜥”。如果你能记住这个助记法以及如何使用它，你在提示词方面的能力将超过80%的人。我们称之为88%吧，因为那是个吉利的中国数字。但如果你想超过98%的人，我还有另一个框架。

这是当你用了“小螃蟹骑巨鬣蜥”框架后，感觉结果还是不太到位时可以用的。你可以用“拉面拯救悲惨白痴（ramen saves tragic idiots）”框架来进一步提升。这个框架的第一部分是重新审视（Revisit）“小螃蟹骑巨鬣蜥”框架。看看你是否可以添加其他东西，比如一个人设，更详细地描述输出，提供更多参考。同时也要考虑去掉一些东西。里面有没有什么相互矛盾的信息可能会让AI感到困惑？框架的第二部分是将提示词分隔（Separate）成更短的句子。和AI交谈就像和人交谈一样，如果你只是像文字呕吐一样说一大堆东西，AI可能会感到困惑。所以你可以考虑把你说的内容分成更短的句子，使其更清晰、更简洁。所以，不要像“吧啦吧啦吧啦吧啦吧啦吧啦吧啦”那样说个不停，你可以说“先这样，然后那样，再这样”。明白了吗？框架的第三部分是尝试（Try）不同的措辞和类比任务。例如，也许你正在让AI帮你写一篇演讲稿，但总感觉不太对劲，你知道吗？就是没抓住重点。所以你可以重新定义这个问题。不要说“帮我写一篇演讲稿”，而是说“帮我写一个故事，来说明你想说明的任何事情”。毕竟，一篇好的演讲稿的核心就是一个引人入胜、有力的故事。

你好，我是来自未来的Tina。我刚从奥斯汀回到香港，看来在倒时差的状态下，我忘了录这个框架的最后一部分。所以我现在来补上，那就是引入约束（Introduce constraints）。你有没有那种朋友，或者你自己就是那种朋友，当有人问“嘿，午餐想吃什么？”他们就说“哦，随便什么都行。”这可帮不上什么忙。同样地，如果你觉得你的AI输出结果不太理想，你可以考虑引入约束，让结果更具体、更有针对性。例如，你正在为穿越德克萨斯的公路旅行制作播放列表，但你就是感觉不对味。你可以引入一个约束，比如“只包含夏日乡村音乐”。这样感觉就对多了。好的，现在回到过去的Tina。

明白了吗？“拉面拯救悲惨白痴”。将这两个框架结合起来，你在提示词方面的能力将超过98%的人。顺便说一句，我也想说，这些框架不是我自己编的。我只对这些酷炫的助记法拥有功劳。实际的框架来自谷歌。所以，如果你想更深入地研究，成为比99%甚至100%的人更擅长提示词的人，我建议你看看我这边的这个视频，我会链接过来，我在视频里总结了谷歌的提示词课程，这是我迄今为止发现的最好的通用提示词课程。另外，我也建议你看看一些针对特定模型的提示词生成器，比如这个来自OpenAI的，这个来自Gemini的，以及这个来自Anthropic的。这些对于生成初稿和充分利用特定模型很有帮助。

对于那些认为提示词作为一项技能将变得过时的人，再想想吧。尤其是在更高级的应用中，比如构建智能体和编码，提示词变得比以往任何时候都更加重要。它就像是把所有东西粘合在一起的胶水，确保你能持续获得你想要的结果。现在，谈到更高级的技能，让我们进入下一个主题，那就是智能体（agents）。

AI智能体是使用AI代表用户追求目标和完成任务的软件系统。当我们提到AI智能体时，我们通常指的是某个特定角色类型的AI版本。例如，一个客服AI智能体应该能够收到一封邮件，比如有人说“我忘了密码，登不进去了”，它应该能够回复那封邮件，并且能够引用网站上的“忘记密码”页面。截至今天，它还不能做所有事情，也无法处理客服人员应该收到的所有问询，但它可以自主处理很多这类通用或常见的问题。同样地，对于一个编码智能体，如果你给它很好的提示，让它构建一个网络应用程序，它应该能够返回一个该应用程序的MVP（最小可行产品）版本。当然，你肯定还需要添加很多东西并进行调整，但它可以编写出第一个版本的代码。

AI智能体是一个吸引了大量兴趣和资金投入的领域，我真的期望它们会随着时间的推移变得越来越好，并融入到各种产品和业务中。事实上，我听过的关于AI智能体最宝贵的建议来自这个YC的视频，那就是：对于每一个SaaS（软件即服务）公司，都会有一个垂直领域的AI智能体版本。你可以想象，每一个SaaS独角兽公司，都可能有一个与之对应的垂直领域AI独角兽。

那么，一个AI智能体到底由什么组成呢？有很多框架，但我见过的最好的一个来自OpenAI。他们列出了构成一个AI智能体的六个组成部分。第一个是实际的AI模型。没有模型就不可能有AI智能体。这是驱动AI智能体推理和决策能力的引擎。第二个是工具（tools）。通过为你的AI智能体提供不同类型的工具，你让它能够与不同的接口互动并访问不同的信息。例如，你可以给你的AI智能体一个邮件工具，让它能够访问你的电子邮件账户并代表你发送邮件。

接下来是知识（knowledge）和记忆（memory）。你可以让你的智能体访问一个关于你公司的特定数据库，这样它就能回答问题并分析你公司的特定数据。记忆在特定类型的智能体中也很重要。比如，如果你有一个治疗师智能体，你和它进行了一次非常棒的会谈，但下一次它完全忘了你们在谈什么，那可能就不太好了。所以，这就是为什么你希望让你的智能体拥有记忆，以便它能记住你们之前进行的所有会谈。

然后是音频（audio）和语音（speech）。这让你的AI智能体能够通过自然语言与你互动，比如能够用多种不同的语言和它交谈。

再来是护栏（guardrails）。如果你的AI智能体失控并开始做一些你意想不到的事情，那可就不好了。所以我们有相应的系统来确保你的AI智能体受到约束。

最后是编排（orchestration）。这些流程可以让你在特定环境中部署你的智能体，监控它们，并随着时间的推移改进它们。在你构建了一个AI智能体之后，你不能就跑掉，希望它自己能正常工作。

说到AI智能体，Retool刚刚推出了其企业级的智能体开发平台。目前，在构建AI演示和真正能在你的业务中发挥作用的AI之间仍然存在巨大差距。Retool可以让你构建连接到你实际系统并采取实际行动的应用程序。你可以使用任何大语言模型，比如Claude、Gemini、OpenAI，任何你想要的。你的智能体可以真正地读写你的数据库，而不仅仅是和你聊天。它还提供端到端的支持，包括测试和邮件来跟踪性能、监控、访问控制等等。这些都是不那么华丽但对于在你的业务中真正实现至关重要的东西。使用Retool和AI的公司已经看到了真正令人印象深刻的成果。例如，德克萨斯大学医学分部将其诊断能力提高了10倍。超过10,000家公司已经在使用Retool。所以，如果你想构建真正有用的AI，而不仅仅是看起来令人印象深刻的，请访问retool.com/tina，描述中也有链接。非常感谢Retool赞助本视频的这一部分。

模型提供智能，工具赋能行动，记忆和知识为决策提供信息，语音和音频实现自然交互，护栏确保安全，而编排则管理着这一切。我也想指出，提示词在智能体方面也真的非常重要，特别是当你构建多智能体系统时，你不仅仅只有一个智能体，而是有一个相互协作的智能体网络。你的提示词需要非常精确，并能产生一致的结果。

那么我们到底如何构建这些AI智能体呢？有哪些技术可用？目前有相当多的无代码和低代码工具可用。我个人认为n8n是通用场景下的最佳选择，而Gumloop则非常适合企业级用例。如果你会编程，我建议你看看OpenAI的Agents SDK，它内置了所有这些组件。或者如果你想要免费的，有谷歌的ADK（Agent Development Kit）。还有专门用于编码智能体的Claude Code SDK。老实说，这些不同的技术和实现方法会随着时间的推移不断变化，我敢肯定在接下来的几个月里，会有更多的智能体构建工具供你使用。这就是为什么我真的建议你专注于这些关于AI智能体组件、不同协议和系统的基础知识，因为这些基础知识不会那么快改变，并且将适用于任何出现的新工具和新技术。所以，如果你想更深入地了解AI智能体，我在这里有一个关于AI智能体基础知识的视频。如果你想开始构建你的AI智能体，我还有另一个视频叫做《构建AI智能体》，你也可以在这里查看。我在里面更详细地介绍了AI智能体。

这些是构成单个AI智能体的组件。但很多时候，你可能也想构建多智能体系统，在这种系统中，你不仅有一个智能体，而是可以有一个协同工作的智能体系统。这样做的原因有点像，如果你的公司里只有一个人试图做所有的事情，结果可能不会很好，对吧？那个人在试图同时管理所有事情时会变得非常困惑。所以，让具有特定角色的人组成公司要好得多。这与智能体非常相似。如果你只有一个智能体试图做所有事情，它会感到困惑，会有很多事情同时发生。所以，通常最好将其分解为具有特定角色的不同子智能体，它们协同工作以获得你想要的结果。如果你想了解更多关于多智能体系统的信息，Anthropic有一篇非常好的文章，我会把链接放在描述里。顺便说一句，我提到的所有资源都会链接在描述中。

你可能也听说过MCP，这是最近很多人在谈论的东西。它也是由Anthropic开发的，基本上是一种标准化的方式，让你的智能体可以访问工具和知识。你可以把它想象成一个通用的USB插头。在MCP之前，让你的智能体访问某些工具实际上相当困难，因为所有不同的网站和API都用不同的方式实现，数据库也是如此。它们的配置都略有不同。所以，把它们与你的智能体连接起来是件很麻烦的事。但是有了MCP，因为有了一个通用的USB插头，你现在可以非常容易地给你的智能体任何类型的工具和任何类型的知识，前提是它遵循MCP协议。

好的，这是关于智能体部分的一个小测验。请在评论中写下答案。

接下来，让我们转向使用AI来构建应用程序，也就是AI辅助编码，也就是氛围感编码（vibe coding）。

在2025年2月，OpenAI的联合创始人Andrej Karpathy发了一条病毒式推文。他说：“出现了一种新的编码方式，我称之为‘氛围感编码’，你完全沉浸于氛围中，拥抱指数级增长，甚至忘记了代码的存在。这之所以可能，是因为大语言模型变得太强大了。你只需告诉AI你想要构建什么，它就会为你处理实现。”在我看来，这就是将AI融入你的产品和工作流程的新方式，用氛围感编码来创造东西。

例如，你可以简单地告诉一个大语言模型：“请为我创建一个简单的React网络应用，名为‘每日心情’（Daily Vibes）。用户可以从一个表情符号列表中选择一个心情，并可选择性地写下一段简短的笔记并提交。在下方显示过去的心情记录列表，包含日期和笔记。”你只需点击回车，大语言模型就会为你编写代码并生成这个应用。瞧，就这样完成了。但这并没有结束。要正确地进行氛围感编码，并产出真正可用且可扩展的产品，仍然有一些技能、原则和最佳实践。

现在，我向你介绍一个氛围感编码的五步框架，助记法是“小雪貂携带危险代码（tiny ferrets carry dangerous code）”。说是危险代码，是因为如果你做得不当，你可能会落得像这边这位老兄一样的下场，他用氛围感编码开发了一个应用，然后因为不了解所谓的版本控制而把所有东西都弄丢了。“小雪貂携带危险代码”代表：思考（Thinking）、框架（Frameworks）、检查点（Checkpoints）、调试（Debugging）和背景（Context）。

思考（Thinking），顾名思义，就是认真思考你到底想构建什么。如果你自己都不知道你到底想构建什么，你怎么能期望AI能够做到呢？在我看来，最好的方法是创建一个叫做产品需求文档（PRD）的东西。在这里，你定义你的目标受众、核心功能以及你将用来构建产品的工具。我会在描述中链接一个PRD的例子，但基本上，你就是要花大量时间来思考你正在尝试构建的东西。

接下来是框架（Frameworks）。无论你想构建什么，很可能以前已经有人构建过非常相似的东西了。所以，与其试图重新发明一切，让AI自己去搞定所有事情，不如通过告诉它使用React、Tailwind或Three.js（如果你在制作3D交互体验）等工具，来引导AI走向正确的方向，构建你的特定产品。但你可能会问，“Tina，如果我连该用什么都不知道，我怎么告诉AI用什么呢？”好问题。AI也可以帮你解决这个问题。当你构建你的PRD时，直接问AI：“我正在尝试构建一个像这样的东西，它非常注重3D动画，我希望它是一个网络应用。构建这类东西的常用框架有哪些？”当你用这种方式提问时，你也在自己学习构建特定东西的常用框架是什么。随着时间的推移，你也会对需要使用什么有更好的把握。在氛围感编码的时代，你可能不需要自己编写所有代码，但了解用于构建不同类型应用程序的常用框架仍然对你大有裨益。你也应该知道项目中不同部分和不同文件是如何相互作用的。这将在你为产品构建越来越复杂的功能时对你大有帮助。

框架的第三步是检查点（Checkpoints）。始终使用像Git或GitHub这样的版本控制，否则东西会坏掉，你会丢失你的进度，你会感到非常非常难过，就像那个用氛围感编码开发了整个应用程序，然后因为不懂版本控制而失去一切的老兄一样。

第四步是调试（Debugging）。你花在调试和修复代码上的时间可能会比构建任何新东西的时间都多。这就是现实。要有条不紊，要有耐心，并引导AI找到需要修复的地方。在调试时，如果你了解文件结构和正在发生的事情，你就能更好地提供具体的指令，告诉AI应该在你的代码库的哪个位置进行调试。当你遇到错误时，第一步就是将错误信息直接复制粘贴到AI中，让它尝试修复。如果是视觉上的问题需要修复，也要为AI提供截图。你给AI的细节和背景信息越多，它就越能更好地找出如何解决问题。

说到背景（Context），框架的最后一部分是背景。无论何时你有疑问，就添加更多的背景信息。总的来说，你给AI提供的背景信息越多，无论你是在构建、调试还是在做任何事情，结果都会越好。这包括为AI提供模型图、例子和截图。记住这个五步框架的助记法是“小雪貂携带危险代码”：思考、框架、检查点、调试和背景。

一个有助于理解这些原则如何在氛围感编码过程中协同工作的思考方式是，意识到你永远只处于两种模式中：要么你在实现一个功能，要么你在调试你的代码。当你在实现功能时，你应该思考如何提供更多背景信息，提及框架，并进行增量更改。在构建新东西时，你总是要一步一个脚印地来。在构建你的产品时，一次只实现一个功能。当你在调试模式下时，你应该思考你项目的底层结构，应该引导AI去改变哪里，以及提供更多的背景信息，比如错误信息和截图。

所以我们现在知道了什么是好的氛围感编码的基础。那么我们实际使用的工具有哪些呢？有一整套开发工具可供选择。在光谱的一端是面向完全初学者的，那些没有工程背景和编码背景的人。一些流行的初学者友好的氛围感编码工具包括lovable、v0和bolt。然后稍微中级一点，我们有像Replit这样的东西。这仍然非常适合初学者，但它也展示了代码库，所以你可以更深入地挖掘，理解项目的结构。再高级一点，你有像Firebase Studio这样的东西。Firebase Studio有两种模式。它有非常用户友好的提示模式，以及一个完整的IDE体验，IDE代表集成开发环境，一个专门为编写和处理代码而设计的界面。在这种情况下，它是在VS Code（一个非常流行的IDE）之上构建的。使用Firebase Studio，你可以在无代码的提示视图和编码模式之间切换。Firebase Studio还有一个好处是免费。

现在转向更高级的氛围感编码工具。这将包括AI代码编辑器和编码智能体，如Windsurf和Cursor。我们之前谈论的所有东西都是基于网络的，所以设置非常简单，环境是隔离的，它为你处理了很多事情。但如果你真的想产出生产级别的、可扩展的代码，那么你通常需要开始迁移到使用像Windsurf和Cursor这样的东西。开发将在你的本地机器上进行。所以设置会稍微复杂一些，但你也可以访问一整套开发工具和不同的功能。对于Windsurf和Cursor，你直接拥有那个编码环境，即IDE。在光谱的最先进的一端，你有命令行工具，比如Cloud Code。这些工具直接存在于你的终端中，在你的计算机的根目录下。使用这些工具，你需要习惯在终端或命令行中工作。但它确实给你带来了更多的功能，你可以将它与任何你选择的IDE一起使用。像Cloud Code这样的工具在你处理复杂的代码库时真正开始大放异彩。但这里的期望是，你确实需要知道如何编码，熟悉你的计算机，并对软件有深入的理解。

好的，这就是关于氛围感编码的速成课程。如果你想更深入地研究这个，我制作了一个完整的视频叫做《氛围感编码基础》，我在里面讲得更详细。我也专门制作了一个关于Firebase Studio的视频，我会链接在这里，还有另一个我谈论Claude for models和Cloud Code的视频，我也会链接在这里。现在我会在屏幕上放一个小测验，看看你是否记住了关于氛围感编码的信息。

最后一部分。未来会是什么样子？在AI世界里，我们不是用年甚至月来衡量事物，我们是用周来衡量。而且时间线变得越来越压缩。当我在Code with Claude会议上时，Anthropic的CEO Dario做了一个很好的类比。他说，这基本上就像被绑在一个穿越时空的火箭上，时间和空间都在扭曲，所以一切都在加速、加速、再加速。特别是因为这个，如果你只是想跟上所有AI新闻，所有新出现的东西，所有新模型，所有新工具，所有新技术，你永远也跟不上所有东西，而且可能在这个过程中变得非常焦虑。

所以，我的建议是，不要太关注所有新出现的东西，而是专注于正在发生的潜在趋势。我认为有三个主要的潜在趋势。第一个是融入工作流程和现有产品。2025年绝对是人们将AI实际融入到他们现有工作流程的一年。一个典型的例子就是谷歌自己。我参加了他们的Google I/O大会，他们正在投入大量精力，通过在各处集成AI来使谷歌产品变得更好。我认为这应该是所有公司的榜样。思考如何通过引入AI来改善你的流程，以获得更好的用户体验并降低成本。在实施方面，如果你学会了如何进行AI辅助编码或氛围感编码，生产力将得到巨大提升。

有了这一整套编码工具，对于那些想创造东西但可能不会编码的人来说，进入门槛大大降低了。但同时，也在大力推动提高开发人员的生产力。在体验了像Cloud Code这样的命令行工具之后，我完全能看到这类工具的巨大好处。我认为未来会大量投入开发和改进命令行工具。所以我认为，如果你是技术人员，或者你愿意学习技术性的东西，学习像Cloud Code这样的命令行工具将会是未来的方向。

最后，对AI智能体的关注丝毫没有减弱。事实上，人们对构建AI智能体的兴趣越来越大，因为AI智能体在改进现有产品和构建新产品方面有巨大的潜力。AI智能体让体验可以个性化，7天24小时可用，而且成本低得多。就像YC说的那样，对于每一个SaaS独角兽公司，很可能都会有一个相应的AI智能体公司。我敢肯定，在接下来的几个月里，会有越来越多的工具让你能够更容易地实现和构建智能体。所以如果你想创造点什么，做个生意，创个业什么的，我建议你研究一下AI智能体。

