本视频将全面介绍您成功入门“感觉式编程”（Vibe Coding）所需的一切知识。我将以极慢的速度讲解，假设您没有任何先前经验，并尽可能详细地逐步解释。此处的目的并非让您成为专业的软件工程师，而是基于我十余年的编程经验，为您提供那些能显著简化您学习和工作的信息片段。

能够成功进行“感觉式编程”的人，至少在编辑器使用、理解不同模型及其适用场景、如何接受代码补全以及各种工具的用途方面具备一些经验。如果这些对您而言尚无概念，请不必担心，我将在本视频中解释。但确实有一些基础知识需要学习，以便您能达到远超普通人群的水平。

接下来，让我们正式开始本视频，我将为纯粹的初学者提供一份“感觉式编程”指南。首先，我来阐释一下“感觉式编程”的具体含义。它主要是指您将高度依赖AI模型或大型语言模型（LLM），例如GPT或Claude这类能够编写和生成代码的流行模型。

这就是“感觉式编程”的核心。您无需在编辑器中亲自编写大量代码，也无需掌握所有语法（即构成编程语言的词汇和规则），只需用自然语言向LLM或AI模型提出需求，由它们为您生成代码。

在本视频中，我假设您不具备编程知识。因此，我不会逐行解释AI模型生成的代码，而是更侧重于工具层面：如何利用代码编辑器？如何利用AI模型？以及如何最终获得一个可用的应用程序？这才是我们的核心目标。

一、准备工作：选择与安装AI代码编辑器

首先，我们需要一个编写代码的平台。正如您使用Microsoft Word或Google Docs撰写文章一样，编程也有专门的工具，称为IDE（集成开发环境）或代码编辑器。近年来，集成了AI模型的AI代码编辑器应运而生，这些模型由顶尖人才构建。

这些AI模型能够处理复杂的任务并为我们生成代码。因此，我们的第一步是下载一款AI代码编辑器。无论您使用Mac、Linux还是Windows系统，都有多种选择。市面上的AI代码编辑器众多，但在本视频中，我推荐您下载名为Cursor的编辑器。

Cursor提供免费版本。如果您决定升级到每月20美元的付费版，体验将显著提升。我个人建议您从免费版开始，评估其是否符合您的需求和兴趣。如果确实需要且频繁使用，再考虑升级到专业版以获得更佳体验。需要声明的是，本视频并非Cursor赞助，我不会因您的购买行为获得任何利益。市面上还有其他优秀的编辑器，如Windsurf（同样有免费和付费版）和Trey AI（某种程度上更自由，提供更多模型选择）。您可以根据喜好选择，但我将使用Cursor，因其目前最为流行，且在YouTube上演示较多。

请您自行下载并安装Cursor。安装过程中，可能会提示您从其他代码编辑器（如VS Code）导入设置。如果您不了解VS Code或没有相关设置，请忽略此提示，选择“全新开始”即可。

二、配置Cursor编辑器与辅助工具

打开Cursor后，其界面可能与我的略有不同。为确保我们同步操作，请首先点击左上角的“文件”(File)菜单，选择“打开文件夹”(Open Folder)。在您熟悉的位置（如桌面）创建一个新文件夹，例如命名为“cursor_tutorial”，然后选中并打开它。这样，您就进入了一个新的项目环境。

我通常会将屏幕布局如下：左侧是Cursor代码编辑器，用于实际开发；右侧打开一个辅助AI工具，如ChatGPT。ChatGPT同样有免费和付费版本。我喜欢使用两个AI模型，分别承担不同任务：右侧的作为研究或顾问AI，用于咨询与代码不直接相关的通用问题；左侧的Cursor则专注于编码。

HubSpot的ChatGPT编程指南
如果您计划使用ChatGPT，我推荐HubSpot提供的免费资源《Code with Chat GPT》。该指南极具实用价值，涵盖了使用ChatGPT进行编程任务的要点，包括针对不同语言的实用提示模板、调试技巧以及构建复杂应用的方法。其中，将ChatGPT用作编程导师的部分尤为有用，能助您快速学习并解答疑难问题。指南还包含工作流程建议、编码专用提示、代码审查最佳实践以及AI辅助改进现有代码的示例。此资源由本视频的赞助商HubSpot免费提供，感谢他们的支持。

三、Cursor编辑器界面与核心功能介绍

Cursor是基于VS Code（一款在AI时代前非常流行的代码编辑器）的一个“分支”(fork)。这意味着Cursor复制了VS Code的开源代码，并添加了新功能，其中最重要的便是右侧的AI聊天窗口。若此窗口未显示，可点击屏幕顶部的“切换窗格”(Toggle the pane)按钮来打开/关闭，并通过拖动侧边栏调整窗口大小。

AI聊天窗口设置：

模式 (Mode):

提问 (Ask) 模式: 此模式下，AI模型不会修改您项目中的任何文件，仅用于提问和获取代码建议，相对安全。

代理 (Agent) 模式: 功能更强大，允许AI创建新文件、运行命令、修改和删除代码，因此风险也更高，但能实际改变项目内容。

手动 (Manual) 模式: (本教程暂不深入)
建议初学者主要使用“提问”和“代理”模式。

模型 (Model):
您可以选择不同的AI模型，如Claude 3.5 Sonnet, Gemini 2.5 Pro, GPT-4等。若不确定，推荐使用Claude 3.5 Sonnet。付费用户或有足够请求额度的用户可以使用性能更强的Claude 3.7 Sonnet。这些模型通常在编码任务上表现优异。

最大模式 (Max Mode):
启用后会为AI提供更多上下文（读取更多文件和代码），仅在大型项目中重要。此功能会产生额外费用，初学者无需启用。

自动模式 (Auto Mode):
若您不愿选择模型，可启用此模式，Cursor将根据任务自动选择合适的模型。

四、项目规划与第一个AI指令

在开始编码前，务必制定清晰的计划。本教程将尝试制作一个类似《超级马里奥兄弟》的浏览器小游戏。我特意选择了一个我不熟悉的领域，以便演示如何在不了解具体编程语言或技术的情况下解决问题。

初步规划 (使用辅助AI，如ChatGPT):
向您的顾问AI（如ChatGPT）描述您的想法，例如：“我想制作一个超级马里奥兄弟类型的浏览器游戏。我不会编程，计划使用Cursor辅助。请帮我制定一个计划，并告诉我开始前需要了解的基础知识。”
AI可能会给出如下建议：

明确需求: 2D横向卷轴平台游戏，角色能行走、跳跃、收集物品，地图滚动等。

技术选型 (Tech Stack):

语言: HTML, CSS, JavaScript。

框架/游戏引擎: AI可能推荐Phaser.js或Kaboom.js等简化游戏开发的框架。这些框架封装了复杂的物理和数学运算。

细化技术选型:
询问AI：“在Phaser.js和Kaboom.js中，哪个更适合使用AI辅助编码的初学者？”
假设AI推荐Kaboom.js因其易用性。AI甚至可能直接提供一个可以复制到Cursor的初始提示。

在Cursor中创建提示文件 (可选):
在Cursor左侧文件浏览器中，点击“新建文件”按钮，创建一个如prompt.txt的文本文件，将AI提供的提示粘贴进去，便于查看和修改。例如：

Generated code
Create an HTML + JavaScript game using Kaboom.js where a player can move left and right and jump on platforms. Use a simple square shape instead of sprites.


(原提示中“Show me the full code for index.html”一行可以酌情删除，以免干扰AI的生成过程。)

向Cursor AI发出指令:

将Cursor的AI聊天模式切换到“代理 (Agent) 模式”，以便AI能够创建和修改文件。

将整理好的提示（如上述prompt.txt中的内容）复制粘贴到Cursor的AI聊天输入框中。

注意：如果直接从Cursor内的文件复制，可能会作为文件引用（如prompt.txt）被粘贴。此时，您可以直接说“执行此文件中的指令”(Do what this file says)，或者从外部文本编辑器复制纯文本内容粘贴。

发送指令，等待AI生成代码。此过程可能需要几分钟，AI可能会创建多个文件（如index.html和game.js）。

五、审查与接受AI生成的代码

AI完成代码生成后，Cursor会显示已更改或创建的文件列表（通常以绿色表示新增行，红色表示删除行）。您需要审查这些文件并决定是“接受”(Accept)还是“拒绝”(Reject)更改。

逐个点击文件查看内容。作为初学者，您可能无法完全理解代码，此时通常选择“接受”。

您可以点击每个文件下方的“接受”按钮，或使用“全部接受”(Accept All)选项。

AI可能还会生成一个README.md文件。.md代表Markdown格式，这是一种人类可读的格式，通常用于解释项目如何运行、代码结构等。

技巧: 选中Markdown文件，按Ctrl+Shift+P (Windows)或Cmd+Shift+P (Mac)打开命令面板，输入“Markdown: Open Preview”可查看其渲染后的效果。

六、运行与调试游戏

运行HTML文件:
根据README.md的指示（或常识），在文件浏览器中找到项目文件夹，双击index.html文件，它将在您的默认网页浏览器中打开。

处理错误 (Debugging):
首次运行很可能会遇到错误。

向AI报告错误: 将浏览器中显示的错误信息准确地复制到Cursor的AI聊天窗口，并描述问题。例如：“当我打开HTML文件时，浏览器报错‘Script error’。”

审查AI的修复: AI会尝试修改代码。再次审查并接受更改。

刷新浏览器: 返回浏览器刷新页面查看效果。

使用浏览器开发者工具:

在浏览器页面上右键，选择“检查”(Inspect)。

在打开的开发者工具中，找到并切换到“控制台”(Console)标签。这里会显示更详细的JavaScript错误信息。

将控制台中的错误信息提供给AI，例如：“仍然无法工作。我在控制台看到错误：‘solid is not defined’。” 同时可以附上游戏界面的截图。

迭代调试: 这个过程（报告错误 -> AI修复 -> 测试）可能会重复多次。这是编程的常态。如果一个AI模型持续无法解决问题，可以尝试在Cursor中切换到另一个模型（如从Claude 3.5切换到Claude 3.7），新开一个聊天窗口，并说明当前遇到的问题，让新模型接手。

手动调试与内联补全:
如果AI难以定位问题，可以尝试：

阅读代码: 尝试理解与问题相关的代码段。例如，搜索与“jump”相关的代码。

使用查找功能: 在代码文件中按Ctrl+F (Windows)或Cmd+F (Mac)进行文本搜索。

内联代码补全/修改: 在可疑代码行附近，按Ctrl+K (Windows)或Cmd+K (Mac)激活内联AI，让它针对特定代码块进行修改或添加调试信息。例如，在跳跃逻辑处让AI加入console.log("Jumping");来检查函数是否被调用。

提供上下文给AI: 选中相关代码段，右键选择“添加到聊天”(Add to Chat)，这样AI的注意力会更集中。

引入外部文档:
如果问题依旧，可能是AI对所用框架（如Kaboom.js）的了解不足。

搜索官方文档: 在Google搜索“Kaboom.js docs”，找到官方文档页面。

将文档添加为Cursor上下文:

在Cursor的AI聊天窗口，点击“添加上下文”(Add Context)按钮。

选择“文档”(Docs)，点击“添加新文档”(Add new doc)，粘贴官方文档页面的URL。

确认后，该文档会成为AI可以参考的知识源。

然后向AI提问，并明确指示它参考此文档：“请阅读此文档以修复跳跃问题。它仍然无法工作。”

AI现在可以结合官方文档来提供更准确的解决方案。

通过上述调试步骤，即使过程曲折，最终也应该能让游戏基本运作起来（例如，角色可以移动和跳跃）。

七、版本控制 (Git)

当项目达到一个可用的阶段时，保存工作非常重要，以防后续修改破坏现有功能。这里介绍使用Git进行版本控制。

安装Git: 在Google搜索“Git”，下载并为您的操作系统安装它。（Cursor/VS Code可能内置，但安装独立的Git更保险）。

初始化仓库:

在Cursor左侧边栏找到类似分支的图标（源代码管理/Source Control）。

点击“初始化仓库”(Initialize Repository)。

暂存更改 (Stage Changes):

在源代码管理面板中，您会看到所有已修改或新增的文件。

点击“+”号或“全部暂存”(Stage All Changes)按钮，将希望保存的更改添加到暂存区。

提交更改 (Commit Changes):

在消息框中输入本次提交的描述信息，例如：“游戏基本功能实现，角色可跳跃”。

点击“提交”(Commit)按钮。

这样就创建了一个项目快照。

后续更改与提交:

当您对代码进行修改（例如，更改游戏标题）后，源代码管理面板会再次显示这些更改。

重复暂存和提交的步骤，为新的更改创建新的快照。

查看历史与还原:

您可以查看提交历史，对比不同版本间的差异。

如果后续修改导致问题，可以方便地将文件或整个项目还原到之前的任一提交状态，例如通过“放弃更改”(Discard Changes)选项。

强烈建议在项目中使用Git，它能有效防止代码丢失或错误修改带来的麻烦。

八、Cursor的高级功能

Cursor规则 (Rules):

打开Cursor设置（通常是齿轮图标）。

找到“规则”(Rules)部分。

规则允许您为AI模型设定全局（适用于所有项目）或项目特定（仅适用于当前项目）的行为准则，避免重复输入相同指令。

用户规则 (User Rules): 全局规则，例如：“确保生成的代码编写良好、组织有序且模块化。使用描述性的变量、函数和类名。”

项目规则 (Project Rules): 为特定项目添加规则，例如：“始终使用Kaboom.js进行游戏生成。”或“确保所有代码和功能都具有可访问性。”

规则可以设置为手动标记应用、代理请求时应用、自动附加到特定文件类型或始终应用。

设置的规则会保存在项目根目录下的.cursor/rules文件夹中。

模型上下文协议 (MCP - Model Context Protocol):
MCP允许AI代理连接到Cursor原生不支持的外部工具和服务（如Figma, Google Drive, 特定网络爬虫等），赋予AI更强大的能力。本教程不深入，但您可以查找相关资料了解其设置和使用。

扩展 (Extensions):

在Cursor侧边栏（可能在左侧或顶部，图标通常是几个方块）找到“扩展”视图。

您可以搜索并安装针对特定语言、框架或工具的扩展，以增强编辑器的功能和开发体验（非AI直接相关）。

例如：JavaScript代码片段、Python支持（由微软开发）、拼写检查器、Prettier（代码格式化工具）。

安装Prettier后，可使用命令面板 (Ctrl+Shift+P / Cmd+Shift+P) 中的“Format Document”命令自动美化代码。

可以询问AI：“在VS Code（Cursor基于VS Code）中，为这个项目推荐哪些有用的扩展？”

全局搜索:

编辑器顶部的小放大镜图标用于在整个项目中（所有文件）进行高级搜索，而不仅仅是当前文件。可以进行替换、匹配大小写、使用正则表达式等。

九、总结与建议

“感觉式编程”并非如网络上展示的那么简单，直接给出宏大提示就能完美成功。更推荐的方式是：

循序渐进: 一步一步来，让AI生成小块、有针对性的代码。

理解错误: 这样更容易理解出现的错误并进行修复。

积极调试: 编程就是不断解决问题的过程，即使使用AI也需要批判性思维。

学习基础: 了解HTML、JavaScript等基础知识能极大帮助您理解和修改AI生成的代码，甚至比AI更快解决问题。

善用工具: 熟练掌握Cursor编辑器的各项功能（如AI聊天、Git、规则、扩展、搜索等）将使您的开发过程更高效。