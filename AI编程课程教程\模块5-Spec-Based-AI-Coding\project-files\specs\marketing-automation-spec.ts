// 营销活动自动化系统规格说明书
// 完整的系统规格定义，用于指导AI驱动的开发过程

export interface SystemSpecification {
  systemOverview: SystemOverview;
  functionalRequirements: FunctionalRequirement[];
  nonFunctionalRequirements: NonFunctionalRequirement[];
  constraints: string[];
  interfaces: InterfaceSpecification[];
  dataModel: DataModelSpecification;
  businessRules: BusinessRule[];
  testingRequirements: TestingRequirement[];
}

export interface SystemOverview {
  name: string;
  version: string;
  description: string;
  stakeholders: string[];
  businessGoals: string[];
  scope: string[];
  assumptions: string[];
  dependencies: string[];
}

export interface FunctionalRequirement {
  id: string;
  name: string;
  description: string;
  priority: 'critical' | 'high' | 'medium' | 'low';
  complexity: 'high' | 'medium' | 'low';
  category: string;
  acceptanceCriteria: string[];
  businessRules: string[];
  inputs: InputSpecification[];
  outputs: OutputSpecification[];
  preconditions: string[];
  postconditions: string[];
  exceptions: ExceptionSpecification[];
  dependencies: string[];
}

export interface NonFunctionalRequirement {
  id: string;
  category: 'performance' | 'reliability' | 'security' | 'usability' | 'scalability';
  requirement: string;
  measurement: string;
  priority: 'critical' | 'high' | 'medium' | 'low';
  testMethod: string;
}

export interface BusinessRule {
  id: string;
  name: string;
  description: string;
  type: 'constraint' | 'derivation' | 'existence' | 'action_enabler';
  condition: string;
  action: string;
  priority: 'critical' | 'high' | 'medium' | 'low';
  source: string;
  examples: string[];
}

// 营销活动自动化系统完整规格
export const marketingAutomationSpecification: SystemSpecification = {
  systemOverview: {
    name: "营销活动自动化系统",
    version: "1.0.0",
    description: "智能化的营销活动全生命周期管理系统，支持活动策划、执行、监控和优化的自动化流程",
    stakeholders: [
      "营销经理 - 制定营销策略和目标",
      "运营专员 - 执行和监控营销活动", 
      "数据分析师 - 分析活动效果和优化建议",
      "系统管理员 - 维护系统运行和安全",
      "客户 - 接收营销信息和参与活动"
    ],
    businessGoals: [
      "提升营销ROI 30%以上",
      "减少人工操作时间 50%",
      "提高活动执行准确率至 99%",
      "实现个性化营销覆盖率 80%",
      "缩短活动上线时间至 1天内"
    ],
    scope: [
      "营销活动策划和配置",
      "多渠道自动化执行",
      "实时监控和告警",
      "效果分析和报告",
      "智能优化建议",
      "A/B测试管理"
    ],
    assumptions: [
      "用户数据已经过清洗和标准化",
      "第三方营销渠道API稳定可用",
      "用户同意接收营销信息",
      "系统运行环境满足性能要求"
    ],
    dependencies: [
      "CRM系统 - 用户数据来源",
      "数据仓库 - 历史数据分析",
      "邮件服务 - 邮件发送",
      "短信服务 - 短信发送",
      "推送服务 - App推送",
      "支付系统 - 优惠券和折扣处理"
    ]
  },

  functionalRequirements: [
    {
      id: "FR001",
      name: "智能活动策划",
      description: "基于历史数据、用户画像和业务目标，自动生成个性化的营销活动方案",
      priority: "critical",
      complexity: "high",
      category: "核心功能",
      acceptanceCriteria: [
        "能够分析至少12个月的历史活动数据",
        "根据目标受众特征生成个性化方案",
        "预测活动效果指标（CTR、CVR、ROI）",
        "提供至少3个备选方案供选择",
        "方案生成时间不超过30秒",
        "预测准确率达到80%以上"
      ],
      businessRules: [
        "预算分配不能超过总预算限制",
        "活动时间不能与现有活动产生冲突",
        "目标受众重叠度不能超过30%",
        "必须包含A/B测试方案设计",
        "风险评估必须包含缓解措施"
      ],
      inputs: [
        {
          name: "campaignObjective",
          type: "CampaignObjective",
          required: true,
          description: "活动目标（品牌推广、销售转化、用户留存等）"
        },
        {
          name: "targetAudience",
          type: "AudienceSegment[]",
          required: true,
          description: "目标受众细分"
        },
        {
          name: "budget",
          type: "BudgetConstraints",
          required: true,
          description: "预算约束条件"
        },
        {
          name: "timeframe",
          type: "TimeRange",
          required: true,
          description: "活动时间范围"
        }
      ],
      outputs: [
        {
          name: "campaignPlan",
          type: "CampaignPlan",
          description: "完整的活动方案"
        },
        {
          name: "predictions",
          type: "PerformancePrediction",
          description: "效果预测"
        },
        {
          name: "alternatives",
          type: "CampaignPlan[]",
          description: "备选方案"
        }
      ],
      preconditions: [
        "用户已登录并具有活动创建权限",
        "历史数据已同步完成",
        "目标受众数据可用"
      ],
      postconditions: [
        "活动方案已保存到数据库",
        "预测结果已记录",
        "审批流程已启动（如需要）"
      ],
      exceptions: [
        {
          name: "InsufficientDataException",
          condition: "历史数据不足以进行预测",
          handling: "使用行业基准数据或提示用户补充信息"
        },
        {
          name: "BudgetConstraintException", 
          condition: "预算约束过于严格无法生成可行方案",
          handling: "提示用户调整预算或目标"
        }
      ],
      dependencies: ["FR003", "FR005"]
    },

    {
      id: "FR002",
      name: "自动化执行引擎",
      description: "按照预设的规则和时间表，自动执行营销活动的各项操作",
      priority: "critical",
      complexity: "medium",
      category: "核心功能",
      acceptanceCriteria: [
        "支持时间、事件、条件三种触发方式",
        "能够执行邮件、短信、推送、价格调整等操作",
        "执行成功率达到99%以上",
        "支持并发执行1000个活动",
        "异常情况自动重试和告警",
        "执行日志完整记录"
      ],
      businessRules: [
        "执行前必须验证预算余额充足",
        "失败操作自动重试最多3次",
        "超出预算限制立即停止执行",
        "用户取消订阅后立即停止发送",
        "执行时间必须在允许的时间窗口内"
      ],
      inputs: [
        {
          name: "executionPlan",
          type: "ExecutionPlan",
          required: true,
          description: "执行计划配置"
        },
        {
          name: "triggerEvent",
          type: "TriggerEvent",
          required: true,
          description: "触发事件"
        }
      ],
      outputs: [
        {
          name: "executionResult",
          type: "ExecutionResult",
          description: "执行结果"
        },
        {
          name: "executionLog",
          type: "ExecutionLog",
          description: "执行日志"
        }
      ],
      preconditions: [
        "活动已通过审批",
        "执行计划已配置完成",
        "相关服务正常运行"
      ],
      postconditions: [
        "执行结果已记录",
        "相关指标已更新",
        "下一步操作已调度（如有）"
      ],
      exceptions: [
        {
          name: "ServiceUnavailableException",
          condition: "第三方服务不可用",
          handling: "切换到备用服务或延迟执行"
        },
        {
          name: "BudgetExceededException",
          condition: "预算超限",
          handling: "立即停止执行并发送告警"
        }
      ],
      dependencies: ["FR001", "FR004"]
    },

    {
      id: "FR003",
      name: "实时监控和告警",
      description: "实时监控活动执行状态和效果指标，异常情况及时告警",
      priority: "high",
      complexity: "medium",
      category: "监控功能",
      acceptanceCriteria: [
        "实时显示活动执行状态",
        "监控关键指标变化趋势",
        "异常情况5分钟内发出告警",
        "支持自定义告警规则",
        "提供实时仪表板展示",
        "历史监控数据保留6个月"
      ],
      businessRules: [
        "关键指标异常变化超过20%触发告警",
        "执行失败率超过5%触发告警",
        "预算消耗速度异常触发告警",
        "用户投诉率超过1%触发告警"
      ],
      inputs: [
        {
          name: "monitoringConfig",
          type: "MonitoringConfiguration",
          required: true,
          description: "监控配置"
        }
      ],
      outputs: [
        {
          name: "monitoringData",
          type: "MonitoringData",
          description: "监控数据"
        },
        {
          name: "alerts",
          type: "Alert[]",
          description: "告警信息"
        }
      ],
      preconditions: [
        "监控配置已设置",
        "数据采集服务正常运行"
      ],
      postconditions: [
        "监控数据已存储",
        "告警已发送给相关人员"
      ],
      exceptions: [
        {
          name: "DataCollectionException",
          condition: "数据采集失败",
          handling: "使用备用数据源或发送系统告警"
        }
      ],
      dependencies: ["FR002"]
    }
  ],

  nonFunctionalRequirements: [
    {
      id: "NFR001",
      category: "performance",
      requirement: "活动创建响应时间不超过5秒",
      measurement: "95%的活动创建请求在5秒内完成",
      priority: "high",
      testMethod: "负载测试，模拟并发创建请求"
    },
    {
      id: "NFR002", 
      category: "reliability",
      requirement: "系统可用性达到99.9%",
      measurement: "月度停机时间不超过43分钟",
      priority: "critical",
      testMethod: "可用性监控和故障注入测试"
    },
    {
      id: "NFR003",
      category: "scalability",
      requirement: "支持1000个并发活动执行",
      measurement: "系统能够同时处理1000个活动而不影响性能",
      priority: "high", 
      testMethod: "压力测试和容量规划"
    },
    {
      id: "NFR004",
      category: "security",
      requirement: "用户数据加密存储和传输",
      measurement: "所有敏感数据使用AES-256加密",
      priority: "critical",
      testMethod: "安全审计和渗透测试"
    }
  ],

  constraints: [
    "必须集成现有CRM系统，不能影响现有业务流程",
    "必须支持多租户架构，确保数据隔离",
    "必须符合GDPR和国内数据保护法规要求",
    "必须支持水平扩展，应对业务增长",
    "必须提供完整的API接口，支持第三方集成",
    "必须支持私有化部署选项",
    "开发周期不能超过6个月",
    "系统维护成本不能超过现有系统的150%"
  ],

  interfaces: [
    {
      name: "Campaign Management API",
      type: "REST",
      version: "v1",
      baseUrl: "/api/v1",
      authentication: "JWT Bearer Token",
      endpoints: [
        {
          method: "POST",
          path: "/campaigns",
          description: "创建营销活动",
          requestSchema: "CampaignCreationRequest",
          responseSchema: "CampaignCreationResponse",
          errorCodes: ["400", "401", "403", "422", "500"]
        },
        {
          method: "GET",
          path: "/campaigns/{id}",
          description: "获取活动详情",
          requestSchema: "CampaignDetailRequest",
          responseSchema: "CampaignDetailResponse",
          errorCodes: ["401", "403", "404", "500"]
        }
      ]
    }
  ],

  dataModel: {
    entities: [
      {
        name: "Campaign",
        description: "营销活动实体",
        attributes: [
          { name: "id", type: "UUID", required: true, primaryKey: true },
          { name: "name", type: "String", required: true, maxLength: 100 },
          { name: "type", type: "CampaignType", required: true },
          { name: "status", type: "CampaignStatus", required: true },
          { name: "budget", type: "Decimal", required: true, min: 0 },
          { name: "startTime", type: "DateTime", required: true },
          { name: "endTime", type: "DateTime", required: true },
          { name: "createdAt", type: "DateTime", required: true },
          { name: "updatedAt", type: "DateTime", required: true }
        ],
        relationships: [
          { name: "executions", type: "OneToMany", target: "Execution" },
          { name: "metrics", type: "OneToMany", target: "Metric" }
        ]
      }
    ]
  },

  businessRules: [
    {
      id: "BR001",
      name: "预算控制规则",
      description: "确保活动预算不超过设定限制",
      type: "constraint",
      condition: "活动执行时",
      action: "检查剩余预算，超限则停止执行",
      priority: "critical",
      source: "财务部门要求",
      examples: [
        "日预算消耗达到90%时发出预警",
        "总预算消耗完毕时立即停止活动"
      ]
    },
    {
      id: "BR002",
      name: "受众重叠控制",
      description: "控制不同活动间的受众重叠度",
      type: "constraint", 
      condition: "创建新活动时",
      action: "检查与现有活动的受众重叠度，超过30%则提示调整",
      priority: "high",
      source: "营销部门最佳实践",
      examples: [
        "同一用户不能同时收到超过2个促销活动",
        "VIP用户的营销频次不能超过每周3次"
      ]
    }
  ],

  testingRequirements: [
    {
      type: "unit",
      coverage: 90,
      description: "所有业务逻辑函数必须有单元测试覆盖"
    },
    {
      type: "integration", 
      coverage: 80,
      description: "所有API接口必须有集成测试"
    },
    {
      type: "e2e",
      coverage: 70,
      description: "主要业务流程必须有端到端测试"
    },
    {
      type: "performance",
      requirements: [
        "负载测试：1000并发用户",
        "压力测试：找到系统瓶颈",
        "容量测试：验证扩展能力"
      ]
    }
  ]
};

// 辅助类型定义
export interface InputSpecification {
  name: string;
  type: string;
  required: boolean;
  description: string;
  validation?: string;
}

export interface OutputSpecification {
  name: string;
  type: string;
  description: string;
}

export interface ExceptionSpecification {
  name: string;
  condition: string;
  handling: string;
}

export interface InterfaceSpecification {
  name: string;
  type: 'REST' | 'GraphQL' | 'WebSocket' | 'gRPC';
  version: string;
  baseUrl?: string;
  authentication?: string;
  endpoints: EndpointSpecification[];
}

export interface EndpointSpecification {
  method: string;
  path: string;
  description: string;
  requestSchema: string;
  responseSchema: string;
  errorCodes: string[];
}

export interface DataModelSpecification {
  entities: EntitySpecification[];
}

export interface EntitySpecification {
  name: string;
  description: string;
  attributes: AttributeSpecification[];
  relationships: RelationshipSpecification[];
}

export interface AttributeSpecification {
  name: string;
  type: string;
  required: boolean;
  primaryKey?: boolean;
  maxLength?: number;
  min?: number;
  max?: number;
}

export interface RelationshipSpecification {
  name: string;
  type: 'OneToOne' | 'OneToMany' | 'ManyToMany';
  target: string;
}

export interface TestingRequirement {
  type: 'unit' | 'integration' | 'e2e' | 'performance' | 'security';
  coverage?: number;
  description?: string;
  requirements?: string[];
}
