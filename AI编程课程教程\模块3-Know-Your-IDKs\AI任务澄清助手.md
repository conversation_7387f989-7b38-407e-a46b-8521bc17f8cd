# AI任务澄清助手 - 模块3：Know Your IDKs

## 📋 文档目标

本文档旨在帮助学习者使用AI工具来识别和澄清知识盲点（IDKs），特别是在提示词工程和需求分析方面的隐性需求和模糊要求。

## 🎯 任务澄清提示词模板

### IDKs识别分析模板

```
你是一位需求分析和知识管理专家，请帮我识别以下任务中的知识盲点（IDKs）：

**当前任务描述**：
[在此处粘贴你的任务描述]

**IDKs深度挖掘**：
1. **显性需求分析**：任务中明确提到的要求有哪些？
2. **隐性需求发现**：可能存在但未明确表达的需求是什么？
3. **假设条件识别**：任务中隐含的假设条件有哪些？
4. **边界条件探索**：极端情况和异常情况考虑了吗？
5. **利益相关者分析**：所有相关的用户和角色都考虑了吗？

**知识盲点检查**：
- 我可能不知道我不知道的技术点是什么？
- 可能被忽略的业务规则有哪些？
- 潜在的风险和挑战是什么？
- 需要进一步澄清的模糊概念有哪些？

**输出要求**：
1. IDKs清单（按优先级排序）
2. 针对每个IDK的澄清问题
3. 建议的信息收集方法
4. 优化后的任务描述
```

### 提示词工程澄清模板

```
作为提示词工程专家，请帮我分析和优化以下提示词任务：

**原始提示词需求**：
[描述你要设计的提示词的目标和场景]

**提示词工程分析**：

**目标澄清**：
- 提示词要解决的具体问题是什么？
- 期望的输出格式和质量标准是什么？
- 目标用户群体的特征是什么？
- 使用场景和上下文环境如何？

**结构优化**：
- 角色设定是否明确？
- 任务描述是否清晰？
- 输出要求是否具体？
- 约束条件是否完整？

**效果评估**：
- 如何衡量提示词的效果？
- 可能的失败模式有哪些？
- 如何进行A/B测试？
- 迭代优化的策略是什么？

**请提供**：
1. 优化后的提示词模板
2. 测试用例和预期输出
3. 效果评估方法
4. 持续改进建议
```

## ✅ 任务检查清单

### IDKs识别检查

- [ ] **需求完整性检查**
  - 所有功能需求都明确了吗？
  - 非功能需求考虑了吗？
  - 业务规则和约束条件清楚吗？
  - 异常情况和边界条件考虑了吗？

- [ ] **利益相关者分析**
  - 所有相关用户角色识别了吗？
  - 每个角色的需求和期望明确吗？
  - 角色间的交互关系清楚吗？
  - 权限和责任分配明确吗？

- [ ] **假设条件验证**
  - 隐含的假设条件识别了吗？
  - 这些假设是否合理？
  - 假设不成立时的应对方案有吗？
  - 假设的验证方法明确吗？

### 提示词工程检查

- [ ] **结构完整性**
  - 角色定义清晰吗？
  - 任务描述具体吗？
  - 输出格式明确吗？
  - 约束条件完整吗？

- [ ] **语言准确性**
  - 用词是否准确无歧义？
  - 指令是否清晰明确？
  - 专业术语使用正确吗？
  - 语言风格是否一致？

- [ ] **效果可测性**
  - 成功标准明确吗？
  - 评估方法可行吗？
  - 测试用例充分吗？
  - 改进方向清楚吗？

### 上下文设计检查

- [ ] **信息层次**
  - 核心信息突出了吗？
  - 支撑信息充分吗？
  - 背景信息适量吗？
  - 信息组织逻辑清晰吗？

- [ ] **场景适配**
  - 使用场景考虑全面吗？
  - 不同场景的差异化处理了吗？
  - 场景切换的逻辑清楚吗？
  - 场景扩展的可能性考虑了吗？

## 🤝 AI协作指南

### IDKs挖掘协作策略

1. **多轮对话挖掘**
   - **第一轮**：基础需求理解和初步分析
   - **第二轮**：深度挖掘隐性需求和假设
   - **第三轮**：边界条件和异常情况探索
   - **第四轮**：验证和完善需求描述

2. **角色扮演方法**
   - **用户角色**：从最终用户角度审视需求
   - **开发者角色**：从技术实现角度分析需求
   - **测试者角色**：从质量保证角度检查需求
   - **产品经理角色**：从商业价值角度评估需求

### 协作最佳实践

- **保持好奇心**：主动探索未知领域
- **质疑假设**：挑战既有的认知和假设
- **系统思考**：从整体角度分析问题
- **迭代优化**：持续改进和完善

## ❓ 常见问题模板

### IDKs发现类问题

```
请帮我发现以下任务中可能存在的知识盲点：
1. 我可能忽略了哪些重要的技术细节？
2. 有哪些隐含的业务规则我没有考虑到？
3. 可能存在哪些我没有意识到的风险？
4. 还有哪些利益相关者我没有考虑？
5. 哪些边界条件和异常情况需要特别关注？
```

### 需求澄清类问题

```
关于需求的完整性和准确性，请帮我确认：
1. 这个需求的真正目的是什么？
2. 成功的标准应该如何定义？
3. 有哪些约束条件我需要考虑？
4. 可能的替代方案有哪些？
5. 如何验证需求的正确性？
```

### 提示词优化类问题

```
关于提示词的设计和优化，请指导：
1. 当前提示词的主要问题在哪里？
2. 如何让指令更加清晰明确？
3. 需要补充哪些上下文信息？
4. 如何设计更好的输出格式？
5. 怎样测试和验证提示词的效果？
```

### 上下文设计类问题

```
在上下文设计方面，请帮助：
1. 哪些背景信息是必需的？
2. 如何平衡信息的完整性和简洁性？
3. 不同场景下的上下文应该如何调整？
4. 如何确保上下文信息的准确性？
5. 上下文的更新和维护策略是什么？
```

## 🚀 任务优化建议

### 基于模块3特点的优化方向

1. **培养质疑精神**
   - 主动挑战既有假设
   - 深入探索未知领域
   - 系统性地分析问题

2. **建立系统方法**
   - 使用结构化的分析框架
   - 建立标准化的检查流程
   - 形成可重复的工作方法

3. **强化迭代思维**
   - 持续改进和优化
   - 基于反馈调整策略
   - 建立学习和成长机制

### IDKs管理策略

1. **知识盲点地图**
   - 系统性地记录已知的未知
   - 定期更新和维护
   - 优先级排序和资源分配

2. **学习计划制定**
   - 针对性的学习目标
   - 渐进式的能力提升
   - 实践验证和应用

3. **反馈机制建立**
   - 及时的效果评估
   - 持续的改进建议
   - 经验总结和分享

## 📝 使用示例

### 示例1：客服系统需求澄清

**原始需求**：
"开发一个智能客服系统"

**IDKs挖掘结果**：
- **隐性需求**：多语言支持、情感识别、人工转接
- **假设条件**：用户有基本的数字素养、网络环境稳定
- **边界条件**：高并发访问、恶意攻击、系统故障
- **利益相关者**：客户、客服人员、系统管理员、业务分析师

**优化后需求**：
"开发一个支持多渠道接入的智能客服系统，具备自然语言理解、情感分析、知识库检索、人工转接等功能，支持中英文双语，能够处理日均10万次对话，响应时间小于2秒，准确率达到85%以上。"

### 示例2：提示词优化案例

**原始提示词**：
"帮我写代码"

**优化后提示词**：
```
你是一位经验丰富的软件工程师，请帮我编写高质量的代码。

任务要求：
- 编程语言：[具体语言]
- 功能描述：[详细功能说明]
- 技术要求：[性能、安全、可维护性要求]
- 代码风格：[遵循的编码规范]

输出格式：
1. 完整的代码实现
2. 关键部分的注释说明
3. 使用示例
4. 可能的优化建议

请确保代码具有良好的可读性、可维护性和性能。
```

## 🎯 预期效果

通过使用本澄清助手，学习者应该能够：

1. **识别知识盲点**：系统性地发现隐藏的需求和假设
2. **完善任务描述**：将模糊的需求转化为清晰的任务
3. **优化提示词设计**：创建更有效的AI交互指令
4. **建立质疑习惯**：培养主动探索和深度思考的能力
5. **提升分析能力**：掌握系统性的需求分析方法

---

*💡 提示：知道自己不知道什么，是智慧的开始。通过系统性地识别和澄清IDKs，你可以大大提高任务执行的成功率和质量。*
