# 小红书内容管理系统

## 项目简介

这是一个基于现代Web技术栈构建的内容管理系统，专门为小红书等社交媒体平台的内容创作和管理而设计。系统采用前后端分离架构，支持多人协作、AI辅助创作、实时预览等功能。

## 技术架构

### 前端技术栈
- **React 18** - 用户界面框架
- **TypeScript** - 类型安全的JavaScript
- **Vite** - 现代化构建工具
- **React Query** - 数据获取和状态管理
- **React Hook Form** - 表单管理
- **React Quill** - 富文本编辑器
- **Socket.IO Client** - 实时通信
- **Tailwind CSS** - 样式框架
- **Framer Motion** - 动画库

### 后端技术栈
- **Node.js** - 服务器运行环境
- **Express.js** - Web应用框架
- **TypeScript** - 类型安全
- **Sequelize** - ORM数据库操作
- **SQLite/MySQL/PostgreSQL** - 数据库支持
- **Socket.IO** - 实时通信
- **JWT** - 身份认证
- **Redis** - 缓存和会话存储
- **Winston** - 日志管理

### 共享模块
- **TypeScript类型定义** - 前后端类型统一
- **常量定义** - 共享配置和枚举
- **工具函数** - 通用业务逻辑

## 项目结构

```
xiaohongshu-cms/
├── frontend/                     # React前端应用
│   ├── public/                   # 静态资源
│   ├── src/
│   │   ├── components/           # React组件
│   │   │   ├── layout/          # 布局组件
│   │   │   ├── content/         # 内容相关组件
│   │   │   ├── template/        # 模板组件
│   │   │   └── common/          # 通用组件
│   │   ├── pages/               # 页面组件
│   │   ├── hooks/               # 自定义Hooks
│   │   ├── services/            # API服务
│   │   ├── utils/               # 工具函数
│   │   ├── types/               # 类型定义
│   │   └── styles/              # 样式文件
│   ├── package.json
│   └── vite.config.ts
├── backend/                      # Node.js后端API
│   ├── src/
│   │   ├── controllers/         # 控制器
│   │   ├── models/              # 数据模型
│   │   ├── routes/              # 路由定义
│   │   ├── middleware/          # 中间件
│   │   ├── services/            # 业务逻辑服务
│   │   ├── utils/               # 工具函数
│   │   └── config/              # 配置文件
│   ├── package.json
│   └── server.js
├── shared/                       # 共享代码
│   ├── types/                   # 共享类型定义
│   └── constants/               # 常量定义
└── docs/                        # 项目文档
```

## 核心功能

### 1. 内容管理
- ✅ 内容创建、编辑、删除
- ✅ 富文本编辑器支持
- ✅ 多平台内容适配
- ✅ 标签和分类管理
- ✅ 内容状态管理（草稿、已发布、已归档）
- ✅ 批量操作支持

### 2. 模板系统
- ✅ 可复用内容模板
- ✅ 模板变量和参数化
- ✅ 平台特定模板
- ✅ 模板分享和评分

### 3. 协作功能
- ✅ 多用户实时协作编辑
- ✅ 评论和建议系统
- ✅ 版本控制和历史记录
- ✅ 权限管理

### 4. AI辅助
- ✅ AI内容生成
- ✅ 智能标题建议
- ✅ 内容优化建议
- ✅ 情感分析

### 5. 数据分析
- ✅ 内容表现统计
- ✅ 用户行为分析
- ✅ 趋势分析报告
- ✅ ROI计算

### 6. 发布管理
- ✅ 定时发布
- ✅ 多平台同步发布
- ✅ 发布状态跟踪
- ✅ 发布失败重试

## 快速开始

### 环境要求
- Node.js >= 16.0.0
- npm >= 8.0.0
- 数据库（SQLite/MySQL/PostgreSQL）

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd xiaohongshu-cms
```

2. **安装后端依赖**
```bash
cd backend
npm install
```

3. **配置环境变量**
```bash
cp .env.example .env
# 编辑 .env 文件，配置数据库和其他服务
```

4. **初始化数据库**
```bash
npm run db:migrate
npm run db:seed
```

5. **启动后端服务**
```bash
npm run dev
```

6. **安装前端依赖**
```bash
cd ../frontend
npm install
```

7. **启动前端应用**
```bash
npm run dev
```

8. **访问应用**
- 前端应用: http://localhost:3000
- 后端API: http://localhost:5000
- API文档: http://localhost:5000/api

## 开发指南

### 代码规范
- 使用TypeScript进行类型检查
- 遵循ESLint和Prettier配置
- 组件使用函数式组件和Hooks
- API使用RESTful设计原则

### 提交规范
```
feat: 新功能
fix: 修复bug
docs: 文档更新
style: 代码格式调整
refactor: 代码重构
test: 测试相关
chore: 构建过程或辅助工具的变动
```

### 分支管理
- `main` - 主分支，用于生产环境
- `develop` - 开发分支，用于集成测试
- `feature/*` - 功能分支
- `hotfix/*` - 紧急修复分支

## API文档

### 认证接口
```
POST /api/auth/login     # 用户登录
POST /api/auth/register  # 用户注册
POST /api/auth/logout    # 用户登出
GET  /api/auth/me        # 获取当前用户信息
```

### 内容管理接口
```
GET    /api/content           # 获取内容列表
POST   /api/content           # 创建新内容
GET    /api/content/:id       # 获取内容详情
PUT    /api/content/:id       # 更新内容
DELETE /api/content/:id       # 删除内容
POST   /api/content/:id/publish # 发布内容
POST   /api/content/batch     # 批量操作
```

### 模板管理接口
```
GET    /api/templates         # 获取模板列表
POST   /api/templates         # 创建新模板
GET    /api/templates/:id     # 获取模板详情
PUT    /api/templates/:id     # 更新模板
DELETE /api/templates/:id     # 删除模板
```

## 部署指南

### 生产环境部署

1. **构建前端应用**
```bash
cd frontend
npm run build
```

2. **配置生产环境变量**
```bash
# 设置NODE_ENV=production
# 配置数据库连接
# 配置Redis连接
# 配置第三方服务API密钥
```

3. **启动生产服务**
```bash
cd backend
npm start
```

### Docker部署
```bash
# 构建镜像
docker-compose build

# 启动服务
docker-compose up -d
```

## 测试

### 运行测试
```bash
# 后端测试
cd backend
npm test

# 前端测试
cd frontend
npm test

# 测试覆盖率
npm run test:coverage
```

### 测试策略
- 单元测试：测试独立的函数和组件
- 集成测试：测试API接口和数据流
- E2E测试：测试完整的用户流程

## 性能优化

### 前端优化
- 代码分割和懒加载
- 图片优化和CDN
- 缓存策略
- Bundle分析和优化

### 后端优化
- 数据库查询优化
- Redis缓存
- API响应压缩
- 连接池管理

## 监控和日志

### 日志管理
- 使用Winston进行结构化日志
- 不同级别的日志记录
- 日志轮转和归档

### 性能监控
- API响应时间监控
- 数据库性能监控
- 错误率统计
- 用户行为分析

## 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交代码变更
4. 创建Pull Request
5. 代码审查和合并

## 许可证

本项目采用MIT许可证，详见LICENSE文件。

## 联系方式

- 项目维护者：[Your Name]
- 邮箱：[<EMAIL>]
- 问题反馈：[GitHub Issues]

---

*这个项目是AI编程学习的实践项目，展示了如何使用AI工具进行多文件项目的协作开发。*
