// 意图识别服务
// 用于分析用户消息的意图、实体和情感

export interface Intent {
  primary: string;
  confidence: number;
  subIntents: string[];
  urgency: 'high' | 'medium' | 'low';
  category: 'inquiry' | 'complaint' | 'support' | 'sales' | 'general';
}

export interface Entity {
  type: string;
  value: string;
  confidence: number;
  position: [number, number];
}

export interface Sentiment {
  emotion: 'positive' | 'neutral' | 'negative';
  intensity: number; // 0-1
  keywords: string[];
}

export interface MessageAnalysis {
  intent: Intent;
  entities: Entity[];
  sentiment: Sentiment;
  complexity: 'simple' | 'medium' | 'complex';
  requiresHuman: boolean;
}

export interface ConversationContext {
  customerId: string;
  previousMessages: Array<{
    role: 'user' | 'assistant';
    content: string;
    timestamp: Date;
    analysis?: MessageAnalysis;
  }>;
  customer: {
    purchaseHistory: string[];
    currentOrders: string[];
    tier: 'regular' | 'vip' | 'premium';
    preferences: Record<string, any>;
  };
  session: {
    startTime: Date;
    channel: 'web' | 'mobile' | 'wechat' | 'phone';
    referrer?: string;
  };
}

// 意图识别服务类
export class IntentRecognitionService {
  private aiService: any; // AI服务接口
  private intentPatterns: Map<string, RegExp[]> = new Map();
  private entityPatterns: Map<string, RegExp> = new Map();

  constructor(aiService: any) {
    this.aiService = aiService;
    this.initializePatterns();
  }

  // 分析用户消息
  async analyzeMessage(
    message: string, 
    context: ConversationContext
  ): Promise<MessageAnalysis> {
    
    const [intent, entities, sentiment] = await Promise.all([
      this.recognizeIntent(message, context),
      this.extractEntities(message),
      this.analyzeSentiment(message)
    ]);

    const complexity = this.assessComplexity(message, intent, entities);
    const requiresHuman = this.shouldEscalateToHuman(intent, sentiment, context);

    return {
      intent,
      entities,
      sentiment,
      complexity,
      requiresHuman
    };
  }

  // 识别用户意图
  private async recognizeIntent(
    message: string, 
    context: ConversationContext
  ): Promise<Intent> {
    
    // 首先尝试基于规则的快速识别
    const ruleBasedIntent = this.recognizeIntentByRules(message);
    if (ruleBasedIntent.confidence > 0.8) {
      return ruleBasedIntent;
    }

    // 使用AI进行深度意图识别
    const prompt = this.buildIntentPrompt(message, context);
    const response = await this.aiService.generate(prompt);
    
    return this.parseIntentResponse(response);
  }

  // 基于规则的意图识别
  private recognizeIntentByRules(message: string): Intent {
    const normalizedMessage = message.toLowerCase();
    
    // 定义意图模式
    const intentRules = [
      {
        intent: 'product_inquiry',
        patterns: [/什么.*产品/, /介绍.*功能/, /有什么.*特点/, /怎么样/, /好用吗/],
        keywords: ['产品', '功能', '特点', '介绍', '怎么样', '好用']
      },
      {
        intent: 'price_inquiry',
        patterns: [/多少钱/, /价格/, /优惠/, /折扣/, /便宜/],
        keywords: ['价格', '多少钱', '优惠', '折扣', '便宜']
      },
      {
        intent: 'order_status',
        patterns: [/订单/, /发货/, /物流/, /快递/, /什么时候到/],
        keywords: ['订单', '发货', '物流', '快递', '到货']
      },
      {
        intent: 'complaint',
        patterns: [/投诉/, /不满/, /差评/, /退款/, /质量问题/],
        keywords: ['投诉', '不满', '差评', '退款', '质量问题'],
        sentiment: 'negative'
      },
      {
        intent: 'technical_support',
        patterns: [/怎么用/, /使用方法/, /故障/, /坏了/, /不工作/],
        keywords: ['怎么用', '使用', '故障', '坏了', '不工作']
      }
    ];

    let bestMatch = { intent: 'general_inquiry', confidence: 0.3 };

    for (const rule of intentRules) {
      let score = 0;
      
      // 模式匹配
      for (const pattern of rule.patterns) {
        if (pattern.test(normalizedMessage)) {
          score += 0.4;
          break;
        }
      }
      
      // 关键词匹配
      const keywordMatches = rule.keywords.filter(keyword => 
        normalizedMessage.includes(keyword)
      ).length;
      score += (keywordMatches / rule.keywords.length) * 0.6;

      if (score > bestMatch.confidence) {
        bestMatch = {
          intent: rule.intent,
          confidence: Math.min(score, 0.95)
        };
      }
    }

    return {
      primary: bestMatch.intent,
      confidence: bestMatch.confidence,
      subIntents: [],
      urgency: this.assessUrgency(message),
      category: this.categorizeIntent(bestMatch.intent)
    };
  }

  // 构建意图识别提示词
  private buildIntentPrompt(message: string, context: ConversationContext): string {
    return `
分析以下客户消息的意图：

客户消息："${message}"

对话历史：
${context.previousMessages.slice(-3).map(msg => 
  `${msg.role}: ${msg.content}`
).join('\n')}

客户信息：
- 客户等级：${context.customer.tier}
- 购买历史：${context.customer.purchaseHistory.length}个订单
- 当前订单：${context.customer.currentOrders.length}个
- 渠道：${context.session.channel}

请识别客户的主要意图，从以下类别中选择：

1. product_inquiry - 产品咨询
   - 产品功能、特点、规格询问
   - 产品适用性咨询
   - 产品对比需求

2. price_inquiry - 价格咨询
   - 价格询问
   - 优惠活动咨询
   - 性价比评估

3. order_status - 订单查询
   - 订单状态查询
   - 物流信息询问
   - 发货时间咨询

4. complaint - 投诉抱怨
   - 产品质量问题
   - 服务不满
   - 退换货要求

5. technical_support - 技术支持
   - 使用方法询问
   - 故障排除
   - 维护保养

6. return_refund - 退换货
   - 退货申请
   - 换货需求
   - 退款咨询

7. general_chat - 一般对话
   - 闲聊
   - 感谢
   - 问候

返回JSON格式：
{
  "primary_intent": "主要意图",
  "confidence": 0.95,
  "sub_intents": ["子意图1", "子意图2"],
  "urgency": "high|medium|low",
  "category": "inquiry|complaint|support|sales|general",
  "reasoning": "判断理由"
}
`;
  }

  // 解析AI返回的意图识别结果
  private parseIntentResponse(response: string): Intent {
    try {
      const parsed = JSON.parse(response);
      return {
        primary: parsed.primary_intent,
        confidence: parsed.confidence,
        subIntents: parsed.sub_intents || [],
        urgency: parsed.urgency || 'medium',
        category: parsed.category || 'inquiry'
      };
    } catch (error) {
      console.error('Failed to parse intent response:', error);
      return {
        primary: 'general_inquiry',
        confidence: 0.5,
        subIntents: [],
        urgency: 'medium',
        category: 'inquiry'
      };
    }
  }

  // 提取实体信息
  private async extractEntities(message: string): Promise<Entity[]> {
    const entities: Entity[] = [];
    
    // 产品名称实体
    const productMatches = this.extractProductEntities(message);
    entities.push(...productMatches);
    
    // 数字实体（价格、数量等）
    const numberMatches = this.extractNumberEntities(message);
    entities.push(...numberMatches);
    
    // 时间实体
    const timeMatches = this.extractTimeEntities(message);
    entities.push(...timeMatches);

    return entities;
  }

  // 提取产品相关实体
  private extractProductEntities(message: string): Entity[] {
    const entities: Entity[] = [];
    const productPatterns = [
      { pattern: /风扇|电扇/g, type: 'product_type' },
      { pattern: /便携|小型|迷你/g, type: 'product_feature' },
      { pattern: /静音|无声/g, type: 'product_feature' },
      { pattern: /USB|充电/g, type: 'product_feature' }
    ];

    for (const { pattern, type } of productPatterns) {
      let match;
      while ((match = pattern.exec(message)) !== null) {
        entities.push({
          type,
          value: match[0],
          confidence: 0.9,
          position: [match.index, match.index + match[0].length]
        });
      }
    }

    return entities;
  }

  // 提取数字实体
  private extractNumberEntities(message: string): Entity[] {
    const entities: Entity[] = [];
    const numberPattern = /(\d+(?:\.\d+)?)\s*(元|块|分|个|台|件)?/g;
    
    let match;
    while ((match = numberPattern.exec(message)) !== null) {
      const unit = match[2] || '';
      let type = 'number';
      
      if (['元', '块', '分'].includes(unit)) {
        type = 'price';
      } else if (['个', '台', '件'].includes(unit)) {
        type = 'quantity';
      }

      entities.push({
        type,
        value: match[1] + unit,
        confidence: 0.95,
        position: [match.index, match.index + match[0].length]
      });
    }

    return entities;
  }

  // 提取时间实体
  private extractTimeEntities(message: string): Entity[] {
    const entities: Entity[] = [];
    const timePatterns = [
      { pattern: /今天|明天|后天|昨天/g, type: 'relative_time' },
      { pattern: /\d{1,2}月\d{1,2}日/g, type: 'date' },
      { pattern: /\d{1,2}:\d{2}/g, type: 'time' },
      { pattern: /上午|下午|晚上|中午/g, type: 'time_period' }
    ];

    for (const { pattern, type } of timePatterns) {
      let match;
      while ((match = pattern.exec(message)) !== null) {
        entities.push({
          type,
          value: match[0],
          confidence: 0.8,
          position: [match.index, match.index + match[0].length]
        });
      }
    }

    return entities;
  }

  // 情感分析
  private async analyzeSentiment(message: string): Promise<Sentiment> {
    // 简单的基于规则的情感分析
    const positiveWords = ['好', '棒', '满意', '喜欢', '不错', '推荐', '感谢'];
    const negativeWords = ['差', '坏', '不好', '失望', '生气', '投诉', '退款'];
    
    const normalizedMessage = message.toLowerCase();
    
    let positiveScore = 0;
    let negativeScore = 0;
    const keywords: string[] = [];

    for (const word of positiveWords) {
      if (normalizedMessage.includes(word)) {
        positiveScore++;
        keywords.push(word);
      }
    }

    for (const word of negativeWords) {
      if (normalizedMessage.includes(word)) {
        negativeScore++;
        keywords.push(word);
      }
    }

    let emotion: 'positive' | 'neutral' | 'negative' = 'neutral';
    let intensity = 0.5;

    if (positiveScore > negativeScore) {
      emotion = 'positive';
      intensity = Math.min(0.5 + (positiveScore * 0.2), 1.0);
    } else if (negativeScore > positiveScore) {
      emotion = 'negative';
      intensity = Math.min(0.5 + (negativeScore * 0.2), 1.0);
    }

    return { emotion, intensity, keywords };
  }

  // 评估消息复杂度
  private assessComplexity(
    message: string, 
    intent: Intent, 
    entities: Entity[]
  ): 'simple' | 'medium' | 'complex' {
    
    let complexityScore = 0;
    
    // 消息长度
    if (message.length > 100) complexityScore += 1;
    if (message.length > 200) complexityScore += 1;
    
    // 实体数量
    if (entities.length > 3) complexityScore += 1;
    if (entities.length > 6) complexityScore += 1;
    
    // 意图置信度
    if (intent.confidence < 0.7) complexityScore += 1;
    
    // 子意图数量
    if (intent.subIntents.length > 1) complexityScore += 1;

    if (complexityScore <= 1) return 'simple';
    if (complexityScore <= 3) return 'medium';
    return 'complex';
  }

  // 判断是否需要转人工
  private shouldEscalateToHuman(
    intent: Intent, 
    sentiment: Sentiment, 
    context: ConversationContext
  ): boolean {
    
    // 高优先级情况
    if (intent.urgency === 'high') return true;
    if (sentiment.emotion === 'negative' && sentiment.intensity > 0.8) return true;
    if (intent.primary === 'complaint') return true;
    
    // 低置信度
    if (intent.confidence < 0.5) return true;
    
    // VIP客户特殊处理
    if (context.customer.tier === 'premium' && intent.category === 'complaint') {
      return true;
    }
    
    // 多轮未解决
    const recentMessages = context.previousMessages.slice(-5);
    const unsolvedCount = recentMessages.filter(msg => 
      msg.role === 'user' && msg.analysis?.intent.category === intent.category
    ).length;
    
    if (unsolvedCount >= 3) return true;

    return false;
  }

  // 评估紧急程度
  private assessUrgency(message: string): 'high' | 'medium' | 'low' {
    const urgentKeywords = ['紧急', '急', '马上', '立即', '投诉', '退款'];
    const normalizedMessage = message.toLowerCase();
    
    for (const keyword of urgentKeywords) {
      if (normalizedMessage.includes(keyword)) {
        return 'high';
      }
    }
    
    return 'medium';
  }

  // 意图分类
  private categorizeIntent(intent: string): Intent['category'] {
    const categoryMap: Record<string, Intent['category']> = {
      'product_inquiry': 'inquiry',
      'price_inquiry': 'inquiry',
      'order_status': 'support',
      'complaint': 'complaint',
      'technical_support': 'support',
      'return_refund': 'support',
      'general_chat': 'general'
    };
    
    return categoryMap[intent] || 'inquiry';
  }

  // 初始化模式
  private initializePatterns(): void {
    // 这里可以加载预定义的模式和规则
    // 实际应用中可能从配置文件或数据库加载
  }
}
