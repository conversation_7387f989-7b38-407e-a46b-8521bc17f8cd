{"name": "xiaohongshu-cms-frontend", "version": "1.0.0", "description": "小红书内容管理系统前端应用", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "type-check": "tsc --noEmit", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "react-query": "^3.39.3", "axios": "^1.6.2", "socket.io-client": "^4.7.4", "react-hook-form": "^7.48.2", "react-quill": "^2.0.0", "quill": "^1.3.7", "react-select": "^5.8.0", "react-datepicker": "^4.25.0", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-beautiful-dnd": "^13.1.1", "recharts": "^2.8.0", "date-fns": "^2.30.0", "lodash": "^4.17.21", "classnames": "^2.3.2", "react-hot-toast": "^2.4.1", "react-modal": "^3.16.1", "react-loading-skeleton": "^3.3.1", "react-intersection-observer": "^9.5.3", "framer-motion": "^10.16.16", "lucide-react": "^0.294.0", "tailwindcss": "^3.3.6", "autoprefixer": "^10.4.16", "postcss": "^8.4.32"}, "devDependencies": {"@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@types/lodash": "^4.14.202", "@types/react-modal": "^3.16.3", "@types/react-beautiful-dnd": "^13.1.8", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-react": "^4.2.1", "typescript": "^5.2.2", "vite": "^5.0.8", "vitest": "^1.0.4", "@vitest/ui": "^1.0.4", "@vitest/coverage-v8": "^1.0.4", "eslint": "^8.55.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "eslint-config-prettier": "^9.1.0", "prettier": "^3.1.1", "@storybook/addon-essentials": "^7.6.6", "@storybook/addon-interactions": "^7.6.6", "@storybook/addon-links": "^7.6.6", "@storybook/addon-onboarding": "^1.0.10", "@storybook/blocks": "^7.6.6", "@storybook/react": "^7.6.6", "@storybook/react-vite": "^7.6.6", "@storybook/test": "^7.6.6", "storybook": "^7.6.6", "jsdom": "^23.0.1"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "eslintConfig": {"root": true, "env": {"browser": true, "es2020": true}, "extends": ["eslint:recommended", "@typescript-eslint/recommended", "eslint-config-prettier"], "ignorePatterns": ["dist", ".eslintrc.cjs"], "parser": "@typescript-eslint/parser", "plugins": ["react-refresh"], "rules": {"react-refresh/only-export-components": ["warn", {"allowConstantExport": true}], "@typescript-eslint/no-unused-vars": "error", "@typescript-eslint/no-explicit-any": "warn"}}, "prettier": {"semi": true, "trailingComma": "es5", "singleQuote": true, "printWidth": 80, "tabWidth": 2, "useTabs": false}, "vitest": {"globals": true, "environment": "jsdom", "setupFiles": ["./src/test/setup.ts"], "coverage": {"reporter": ["text", "json", "html"], "exclude": ["node_modules/", "src/test/", "**/*.d.ts", "**/*.config.*"]}}}