// 小红书文案模板
const xia<PERSON>ongshuTemplate = {
    systemPrompt: `你是一位专业的小红书文案创作者，擅长写种草文案。

写作风格要求：
- 语气活泼年轻化，多使用emoji
- 突出产品的实用价值和颜值
- 包含具体的使用场景
- 字数控制在150-200字
- 结尾要有互动性的问题或话题
- 使用小红书流行的表达方式和词汇

文案结构：
1. 吸引眼球的开头（使用emoji和感叹号）
2. 产品亮点总结（使用bullet points）
3. 个人使用体验分享
4. 互动引导和话题标签`,

    userPrompt: `请为以下产品写一段小红书种草文案：

产品信息：
- 名称：{productName}
- 类别：{productCategory}
- 目标用户：{targetAudience}
- 核心卖点：{keyFeatures}

要求：
1. 开头要有吸引眼球的hook，使用emoji
2. 中间详细描述使用体验，要具体生动
3. 结尾引导互动，提出相关问题
4. 适当使用emoji增加活泼感
5. 包含3-5个相关话题标签
6. 语气要亲切自然，像朋友推荐一样

示例风格参考：
"🌟这个XX真的太绝了！最近用了一周，简直是XX神器！✨
🔥亮点总结：
• 特点1
• 特点2  
• 特点3
用了之后真的爱不释手！具体使用感受...
姐妹们，你们有用过类似的产品吗？评论区分享一下呀～
#相关标签"`,

    // 不同类别的专用词汇和表达
    categoryKeywords: {
        electronics: ['黑科技', '神器', '续命', '效率神器', '数码好物'],
        fashion: ['绝美', '显瘦', '气质', '时髦精', '穿搭博主'],
        beauty: ['绝绝子', '神仙', '变美', '护肤达人', '美妆博主'],
        home: ['居家好物', '生活品质', '幸福感', '精致生活', '家居控']
    },

    // 常用的小红书表达方式
    expressions: [
        '真的太绝了',
        '简直是神器',
        '爱不释手',
        '强烈推荐',
        '姐妹们冲',
        '闭眼入',
        '不踩雷',
        '真香警告',
        '种草了',
        '必须拥有'
    ],

    // 互动引导模板
    interactionTemplates: [
        '姐妹们，你们有用过类似的{productCategory}吗？',
        '评论区分享一下你们的{productCategory}推荐呀～',
        '有同款的姐妹吗？使用感受如何？',
        '你们还有什么好用的{productCategory}推荐？',
        '想知道更多关于{productName}的问题，评论区告诉我～'
    ]
};

// 导出模板（如果在模块化环境中使用）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = xiaohongshuTemplate;
}
