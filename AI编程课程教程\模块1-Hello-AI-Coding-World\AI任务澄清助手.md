# AI任务澄清助手 - 模块1：Hello AI Coding World

## 📋 文档目标

本文档旨在帮助学习者使用AI工具来生成、检查和澄清模块1的学习任务，特别是围绕BIG THREE（Context、Prompt、Model）核心概念的理解和应用。

## 🎯 任务澄清提示词模板

### 基础任务分析模板

```
你是一位AI编程教育专家，请帮我分析以下学习任务的清晰度和完整性：

**当前任务描述**：
[在此处粘贴你的任务描述]

**分析要求**：
1. 任务目标是否明确？
2. 成功标准是否可衡量？
3. 所需的输入信息是否完整？
4. 预期的输出格式是否清晰？
5. 任务的复杂度是否适合初学者？

**BIG THREE相关检查**：
- Context（上下文）：任务是否提供了足够的背景信息？
- Prompt（提示词）：任务指令是否清晰明确？
- Model（模型）：是否明确了要使用的AI模型或工具？

请提供：
1. 任务清晰度评分（1-10分）
2. 具体的改进建议
3. 重新表述的任务描述
4. 可能遗漏的关键信息
```

### BIG THREE专项澄清模板

```
作为BIG THREE概念的专家，请帮我澄清以下任务中的Context、Prompt、Model要素：

**任务背景**：
[描述你要完成的具体任务]

**请分析并补充**：

**Context（上下文）分析**：
- 当前提供的上下文信息有哪些？
- 还需要补充哪些背景信息？
- 如何构建更完整的上下文？

**Prompt（提示词）优化**：
- 当前的指令是否清晰？
- 如何改进提示词的结构？
- 需要添加哪些具体要求？

**Model（模型）选择**：
- 这个任务最适合使用哪种AI模型？
- 为什么选择这个模型？
- 有哪些替代方案？

**输出格式**：
请提供优化后的完整任务描述，包含清晰的Context、精确的Prompt和合适的Model选择。
```

## ✅ 任务检查清单

### 基础检查项目

- [ ] **任务目标明确性**
  - 任务要达成的具体目标是什么？
  - 成功完成的标准是什么？
  - 预期的学习成果是什么？

- [ ] **输入信息完整性**
  - 所有必需的背景信息都提供了吗？
  - 技术要求和约束条件明确吗？
  - 参考资料和示例充足吗？

- [ ] **输出要求清晰性**
  - 期望的输出格式明确吗？
  - 质量标准和评估标准清楚吗？
  - 交付物的具体要求明确吗？

### BIG THREE专项检查

- [ ] **Context（上下文）检查**
  - 业务背景和应用场景清楚吗？
  - 用户角色和需求明确吗？
  - 技术环境和约束条件完整吗？
  - 相关的领域知识充足吗？

- [ ] **Prompt（提示词）检查**
  - 指令语言清晰明确吗？
  - 任务步骤逻辑合理吗？
  - 特殊要求和注意事项明确吗？
  - 示例和模板提供了吗？

- [ ] **Model（模型）检查**
  - 选择的AI模型适合任务吗？
  - 模型的能力和限制了解吗？
  - 备选方案考虑了吗？
  - 模型使用方法明确吗？

## 🤝 AI协作指南

### 有效协作步骤

1. **初始任务提交**
   - 将原始任务描述完整提供给AI
   - 说明你的背景知识水平
   - 明确你希望得到的帮助类型

2. **迭代澄清过程**
   - 根据AI的反馈逐步完善任务描述
   - 主动询问不明确的地方
   - 要求AI提供具体的改进建议

3. **验证和确认**
   - 让AI重新表述澄清后的任务
   - 确认理解的一致性
   - 验证任务的可执行性

### 协作最佳实践

- **保持开放态度**：接受AI的建议和质疑
- **主动提问**：不要害怕暴露知识盲点
- **逐步细化**：从大框架到具体细节
- **反复验证**：确保理解的准确性

## ❓ 常见问题模板

### 任务理解类问题

```
请帮我确认以下理解是否正确：
1. 这个任务的核心目标是 [你的理解]，对吗？
2. 我需要使用 [具体技术/工具] 来完成，是这样吗？
3. 最终的交付物应该是 [你的理解]，正确吗？
4. 评估标准主要包括 [你的理解]，还有其他吗？
```

### BIG THREE应用类问题

```
关于BIG THREE的应用，我想确认：
1. 在这个任务中，Context应该包含哪些关键信息？
2. Prompt的设计重点应该放在哪些方面？
3. 选择AI模型时应该考虑哪些因素？
4. 如何验证BIG THREE的应用效果？
```

### 技术实现类问题

```
在技术实现方面，请帮我澄清：
1. 需要掌握哪些前置技能？
2. 可能遇到的主要技术难点是什么？
3. 有哪些可用的工具和资源？
4. 如何分步骤完成这个任务？
```

## 🚀 任务优化建议

### 基于模块1特点的优化方向

1. **强化BIG THREE理解**
   - 确保每个任务都明确体现Context、Prompt、Model三要素
   - 提供具体的应用场景和实例
   - 设计对比实验来理解三要素的重要性

2. **注重实践应用**
   - 从简单的"Hello World"开始
   - 逐步增加复杂度和实用性
   - 结合真实的业务场景

3. **建立正确的学习习惯**
   - 培养系统性思考的习惯
   - 强调理论与实践的结合
   - 建立持续学习的意识

### 任务设计优化策略

1. **渐进式复杂度**
   - 从单一概念到综合应用
   - 从模仿到创新
   - 从个人练习到团队协作

2. **多样化场景**
   - 覆盖不同的应用领域
   - 包含不同的技术栈
   - 考虑不同的用户角色

3. **反馈机制**
   - 提供及时的进度反馈
   - 建立同伴评议机制
   - 设计自我评估工具

## 📝 使用示例

### 示例1：任务澄清前后对比

**原始任务**：
"学习AI编程基础"

**使用澄清助手后**：
"通过实现一个简单的AI问答系统，掌握BIG THREE（Context、Prompt、Model）核心概念的应用。具体要求：使用GPT-4模型，设计包含用户背景、问题类型、回答风格的上下文，编写清晰的提示词模板，实现能够根据不同场景提供个性化回答的系统。成功标准：系统能够在至少3个不同场景下提供准确、相关的回答。"

### 示例2：BIG THREE要素澄清

**任务**：创建一个AI代码审查助手

**澄清结果**：
- **Context**：软件开发团队环境，需要审查JavaScript代码，关注代码质量、安全性、性能
- **Prompt**：结构化的审查指令，包含检查项目、输出格式、严重程度分级
- **Model**：选择Claude-3（擅长代码分析），备选GPT-4（通用能力强）

## 🎯 预期效果

通过使用本澄清助手，学习者应该能够：

1. **明确任务目标**：清楚知道要做什么、为什么做、怎么做
2. **完善任务描述**：补充遗漏的关键信息，消除歧义
3. **优化执行策略**：选择合适的工具和方法
4. **建立评估标准**：明确成功的衡量指标
5. **提高执行效率**：减少返工和迭代次数

---

*💡 提示：任务澄清是成功完成学习目标的关键第一步。充分利用AI工具的分析能力，可以帮助你更好地理解和完成学习任务。*
