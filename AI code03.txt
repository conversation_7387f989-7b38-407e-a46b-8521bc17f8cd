在过去的四年里，我开发了超过14款不同的应用程序，而且其中99%的应用程序我都是唯一的开发者。我是一个人独立开发应用的。在过去的四年里，编码和应用构建的格局发生了巨大变化，主要是由于LLM和AI驱动的编码技术的引入。我，你知道，不是想炫耀什么，但我确实认为自己是所有AI编码相关事物的早期采用者。
我很早就开始使用GitHub Copilot，也很早就开始使用Cursor，以及所有这些AI驱动的代码编辑器。考虑到这一点，在过去的一年里，我确实感觉自己找到了一个适合我作为独立开发者的高效工作流程，帮助我非常快速地构建大量应用程序。这就是我想在这个视频中讨论的内容。
我想谈谈我的人工智能驱动的独立开发工作流程，以便快速构建应用程序。那么，让我们开始吧。现在，在这个视频中，我确实使用Cursor作为我选择的AI代码编辑器，但我并没有得到他们的赞助。我与他们没有任何关联。这只是我使用的工具。但是，我在这个视频中谈到的任何关于工作流程的内容，都应该适用于你选择的任何AI驱动的代码编辑器。
现在我有一个完整的独立视频，讲述了我寻找应用创意并进行构建的整个过程，你绝对应该自己去看看那个视频。所以，流程的第一步，假设你有一个想要构建的应用程序。流程的第一部分是设计应用程序并确定其用户界面（UI）层。
我最初不太关注数据库、整体的模式（schema）。第一步是极少的编码。全是UI探索，因为在2025年，也就是拍摄这个视频的时候，我确实相信应用程序的质量标准已经提高了很多。通常，最容易体现质量的标志之一就是用户与你的应用互动时所经历的UI和用户体验（UX）。
然后，一旦我设计好了UI，我才开始考虑整个后端流程。因此，对于我想构建的应用程序，我会立即开始寻找该领域的其他竞争对手。就我个人而言，我只构建那些已经得到市场验证、有证据表明人们愿意为该产品付费的应用程序。
我有一个完整的视频，就在这里，详细介绍了我的整个应用构建理念。我相信它的标题是《我如何独立构建能真正赚钱的应用》。类似这样的。去看看那个视频，我讲得更深入。但长话短说（TL;DR）的总结是，我只构建那些世界上已经存在并且拥有成熟商业模式的应用程序。
所以因为我这样做，我会立即去研究所有其他竞争对手，注册他们的应用程序，看看他们使用的各种UI灵感和UI流程。然后我就开始在脑海中建立一个心智模型，关于我喜欢什么，不喜欢什么。
而且我还喜欢截取大量不同应用程序的屏幕截图，并将它们存放到我笔记本电脑的某个文件夹里，因为我稍后会在我的AI代码编辑过程中使用这些截图。我以前有个坏习惯，就是完全不设计应用程序，而是直接开始构建。
但我发现自己构建了UI的某个初始迭代版本后，意识到，哦不，这看起来真的很糟糕。然后我会回去尝试一次又一次地重新设计。所以现在我非常乐意在开始时多花些精力，争取把UI做得更好一些。
我根本不是一个好的设计师。老实说，我的设计眼光很差。而且我 Figma 用得烂透了。我在 Figma 上简直是垃圾。我曾多次尝试学习如何使用它，但我用起来就是那么慢，感觉效率非常低。但现在随着AI编码的兴起，也出现了AI驱动的设计工具。
所以我使用一个名为 magicpatterns.com 的工具。它是一个AI驱动的设计工具，有点像AI驱动的Figma，但它能直接生成代码。这是一款非常棒的AI驱动的原型设计工具，我最近一直在用它来创建我应用的通用UI模型。所有操作都通过自然语言完成，我告诉它构建什么。
我可以通过自然语言聊天的方式要求它进行编辑。很棒的产品，10分满分。而且，我大学里的一位好朋友实际上也是创始人之一。这不是赞助。他完全没有付钱让我宣传这个。他甚至不知道我会在这个视频里谈论这个产品。我只是一个忠实粉丝，我自己也付费使用这个产品。
然后在Magic Patterns中，你还可以把你之前截取的竞争对手以及通过Mobin获得的那些截图，全部导入到Magic Patterns中，以确定你的UI流程具体会是什么样子。所以这是第一步。我花了相当多的时间来琢磨这个设计过程。
然后，一旦我确定了我喜欢的UI，我就会开始规划后端数据结构会是什么样子。这时我就会打开我的AI驱动的代码编辑器。现在用Cursor，我相信所有其他AI驱动的代码编辑器也一样，你可以将MCPs（模型上下文协议）添加到你的代码编辑器中，以便让LLM更好地访问你正在使用的各种其他工具。
就我个人的技术栈而言，我使用Next.js，后端则使用Superbase。我几乎在我应用的所有方面都使用Superbase。我用它来进行身份验证、存储、数据库，时不时也用它的函数，比如边缘函数、Webhooks等等。我大概在过去一两年里一直是Superbase的长期用户，它已经成为我构建的所有应用程序中的核心组件。
最棒的是，Superbase实际上有一个MCP，你可以将其插入到你选择的AI代码编辑器中，像我之前说的，我用的是Cursor。通过这样做，你可以让你的AI代码编辑器访问并了解你的Superbase数据库模式（schema）是什么样的？你的Postgres表是如何布局的？所以现在我把我的Superbase MCP连接到我的Cursor编辑器中。
然后从这里开始，我开始搞清楚正在进行的系统的设计。所以现在，将Superbase MCP接入我的Cursor代码编辑器后，我就开始规划整个后端流程的系统设计会是什么样子。后端数据库表会是什么样子？而LLM非常擅长系统设计。
在这一点上，我通常已经很清楚大致的流程，比如，好吧，我要创建这个用户表，然后这个用户表会链接到那个表，等等等等。但有时我不得不去构建一个全新的、我不太熟悉的功能，在确定系统设计方面会遇到一些麻烦。
特别是数据库表的设计会是什么样子。这时候我就会开始使用Cursor内的LLM聊天功能来确定数据库模式设计应该是什么样子。我描述这个功能。然后我告诉Cursor，嘿，这就是我想构建的。然后从这里开始，我打开Cursor内的聊天窗口，并将其连接到我的Superbase MCP，然后我就开始来回聊天，讨论数据库设计应该是什么样子。
但我也使用Cursor来帮助我生成应用程序架构的系统设计，它在这方面做得非常出色。所以正如你在这里看到的，Cursor不仅在UI和UX的设计过程中帮助我，它还在系统设计、数据库设计的各个方面帮助我。
在使用Cursor时，我非常提倡使用大量的Cursor规则。我想幸运的是，大约一周前，Cursor刚刚发布了一个全新的工具，只需一个简单的命令就能自动为你生成这些Cursor规则。生成Cursor规则非常方便，因为它为Cursor提供了关于你的应用程序究竟是什么、你使用的技术栈、整体产品结构、你的编码约定、编码习惯等额外上下文。
如果你没有在你的代码编辑器中使用Cursor规则，这会非常非常有用的。强烈建议你这样做。好了，现在我来回聊天。我已经弄清楚了系统设计、后端架构会是什么样子。现在我就要真正开始构建了。以下是我使用AI帮助我构建应用程序的一些通用策略，这些应用程序通常是用Next.js编写的，后端同样由Superbase提供身份验证等功能。
每当我使用LLM帮助我编码构建任何功能时，我添加到每个提示语末尾的第一件事就是：“在你没有95%的把握知道要构建什么之前，不要做任何更改。不断问我后续问题，直到你获得那种信心。”我在末尾添加的这短短一句话，极大地提高了LLM为我编写代码的性能。
因为如果你不加那句话，有时候LLM会有点过于自负。它们会说：“是的，兄弟。我能搞定。”然后你会想：“哇哦，冷静点。”它们往往不知道自己在做什么，会写出大量混乱的代码（spaghetti code），并不真正理解你的要求。所以我确实发现，在末尾加上那个小提示，比如“嘿，在你真正确信你知道要构建什么之前，尽管问我任何澄清性的问题”，这非常有帮助。
强烈建议你在开始使用LLM编码时，将这句话添加到你所有的提示语中。我通常做的另一个通用技巧是，不要让它一次做太多事情。我认为如果你在一个提示中要求它构建太多的东西，它会有点困惑。相反，如果你有一个巨大的功能想要构建，就让它一次只构建一件事，一步一步来，比如一个UI模块、一个函数、一个API路由，一次一步，并保持所有内容都在那一个巨大的聊天上下文中。
这自然引出我的下一个技巧，那就是我尽量避免尽可能少地创建新的聊天。每当我尝试构建某个特定功能时，不要创建一个全新的聊天。我尝试在同一个聊天上下文中完成尽可能多的工作，因为你在整个聊天过程中提供的细节越多、时间越长，它就能为LLM提供更多的上下文，而上下文越多，我确实发现它的表现会更好，并且更了解你到底想构建什么以及它过去犯过的错误。
所以，我尽我所能将所有可能的内容都保留在同一个聊天记录、同一个聊天对话中，而不是开始一个新的。因为当你开始一个新的聊天时，你会丢失上下文，基本上又从零开始了。此时，如果你完全按照我的工作流程操作，你应该既有你喜欢的各种应用程序的屏幕截图，也有来自Magic Patterns的带有真实代码的实际设计。
所以，从这里开始，我的做法是，在Magic Patterns中，它们实际上有你可以直接复制粘贴到你的应用程序中的真实代码。这一点非常非常有用。我典型的工作流程是，我先过一遍Magic Patterns，过一遍我所有的UX设计、UI设计，然后从这里选出我喜欢的所有部分，然后把那些代码直接复制到我的AI代码编辑器里。
它已经为我写好了一切。然后，如果我想做任何进一步的修改，因为你知道，有时AI代码编辑器里的东西和实际设计看起来可能不一样。也许你当下想改变一些东西。这时候我就会开始导入我之前拍的各种屏幕截图和图片，把它们也放到我的代码编辑器里，然后说：“嘿，做些修改让它看起来更像这个，或者修改那个特定的代码组件。”我每次都会在我的AI代码编辑器中附上大量的图片。
我认为这能为它们提供更多关于究竟发生了什么的上下文。尤其是在处理UI bug时，如果我看到代码编辑器尝试构建某个东西，但看起来有点奇怪，我总是会截图并上传到我的聊天对话中，说：“嘿，现在看起来是这样的。这看起来不对。请修复它。”我个人感觉，每当我尝试修复UI bug时，附上一张图片得到的结果，要比仅仅要求LLM在没有任何实际外观上下文的情况下修复我的代码要好得多。
我基本上对构建的每一个新功能都重复这整个过程。
我看看竞争对手在做什么。我看看我喜欢什么。把它们导入我的AI设计工具Magic Patterns。确定我想要推进的UX。然后一旦我确定了UI，我就会弄清楚后端实现会是什么样子，新的数据库表会是什么样子，架构会是什么样子。
我是通过将我的Superbase MCP连接到Cursor，来回聊天以确定正确的系统设计来实现这一点的。然后，在实施那些后端更改的最后阶段，我将所有这些整合起来，并通过截取UI bug的屏幕截图，将其上传到我的聊天中，不断地以这种方式迭代，并将所有内容都保留在同一个聊天窗口、同一个聊天上下文中，从而持续修复bug。
然后，我通过截取UX bug的屏幕截图，将它们放入一个巨大的聊天上下文中，最大限度地减少我创建的新聊天的数量，并不断地来回迭代，直到最终一切都搞定，从而进一步迭代所有前端设计和前端工作。
所以这就是我的AI赋能的独立开发流程。希望你喜欢。希望你觉得有用。如果你有任何问题、想法或意见，请在下面的评论中告诉我。但今天视频的内容就这些了。非常感谢观看，我们下期再见。