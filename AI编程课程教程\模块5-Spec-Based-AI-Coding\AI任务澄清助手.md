# AI任务澄清助手 - 模块5：Spec-Based AI Coding

## 📋 文档目标

本文档旨在帮助学习者使用AI工具来创建、完善和验证规格说明书，确保基于规格的AI编程任务清晰、完整、可执行。

## 🎯 任务澄清提示词模板

### 规格说明书分析模板

```
你是一位软件工程和需求分析专家，请帮我分析和完善以下规格说明书：

**当前规格说明书**：
[在此处粘贴你的规格说明书内容]

**规格完整性分析**：
1. **SMART原则检查**：规格是否具体、可衡量、可达成、相关、有时限？
2. **可测试性评估**：每个需求是否都可以设计测试用例验证？
3. **一致性检查**：不同部分的规格是否存在冲突？
4. **完整性验证**：是否遗漏了重要的功能或非功能需求？
5. **可追溯性确认**：需求与业务目标的关联是否清晰？

**规格质量评估**：
- 语言表达是否清晰无歧义？
- 技术细节是否适当？
- 约束条件是否明确？
- 验收标准是否具体？

**输出要求**：
1. 规格质量评分（1-10分）
2. 具体的改进建议
3. 补充的规格内容
4. 优化后的规格说明书
5. 对应的测试策略建议
```

### 需求验证澄清模板

```
作为需求验证专家，请帮我验证以下需求的正确性和完整性：

**需求描述**：
[描述具体的功能或非功能需求]

**验证维度分析**：

**正确性验证**：
- 需求是否准确反映了用户的真实需要？
- 需求描述是否与业务目标一致？
- 技术实现是否可行？
- 成本效益是否合理？

**完整性验证**：
- 所有必要的功能都包含了吗？
- 非功能需求考虑充分吗？
- 异常情况和边界条件覆盖了吗？
- 接口和集成需求明确吗？

**一致性验证**：
- 不同需求之间是否存在冲突？
- 术语使用是否一致？
- 优先级设置是否合理？
- 依赖关系是否清晰？

**可验证性检查**：
- 如何测试这个需求？
- 验收标准是否明确？
- 测试数据如何准备？
- 成功标准如何衡量？

**请提供**：
1. 需求验证报告
2. 发现的问题和风险
3. 改进建议和解决方案
4. 验证测试计划
```

## ✅ 任务检查清单

### 规格说明书质量检查

- [ ] **SMART原则符合性**
  - Specific（具体）：需求描述具体明确吗？
  - Measurable（可衡量）：成功标准可量化吗？
  - Achievable（可达成）：技术实现可行吗？
  - Relevant（相关）：与业务目标相关吗？
  - Time-bound（有时限）：完成时间明确吗？

- [ ] **内容完整性**
  - 功能需求完整吗？
  - 非功能需求充分吗？
  - 约束条件明确吗？
  - 假设条件清楚吗？

- [ ] **表达清晰性**
  - 语言表达清晰吗？
  - 专业术语准确吗？
  - 逻辑结构合理吗？
  - 格式规范统一吗？

### 需求验证检查

- [ ] **正确性验证**
  - 需求来源可靠吗？
  - 用户确认过吗？
  - 业务价值明确吗？
  - 技术可行性验证了吗？

- [ ] **完整性验证**
  - 所有场景覆盖了吗？
  - 异常情况考虑了吗？
  - 接口需求明确吗？
  - 数据需求完整吗？

- [ ] **一致性验证**
  - 内部逻辑一致吗？
  - 术语使用统一吗？
  - 优先级合理吗？
  - 依赖关系清晰吗？

### 可测试性检查

- [ ] **测试设计**
  - 每个需求都可测试吗？
  - 测试用例可设计吗？
  - 测试数据可准备吗？
  - 测试环境可搭建吗？

- [ ] **验收标准**
  - 成功标准明确吗？
  - 失败条件清楚吗？
  - 性能指标具体吗？
  - 质量标准可衡量吗？

## 🤝 AI协作指南

### 规格驱动开发协作策略

1. **分层协作方法**
   - **需求层**：使用AI分析和完善业务需求
   - **设计层**：使用AI生成系统设计方案
   - **实现层**：使用AI生成符合规格的代码
   - **测试层**：使用AI生成测试用例和验证方案

2. **迭代完善流程**
   - **初稿生成**：使用AI生成规格说明书初稿
   - **质量评估**：使用AI评估规格质量
   - **问题识别**：使用AI识别潜在问题
   - **改进优化**：使用AI提供改进建议
   - **验证确认**：使用AI验证最终规格

### 协作最佳实践

- **结构化思维**：使用标准的规格模板和框架
- **迭代改进**：通过多轮对话逐步完善规格
- **多角度验证**：从不同角度验证规格的正确性
- **可追溯管理**：建立需求与实现的追溯关系

## ❓ 常见问题模板

### 规格完善类问题

```
关于规格说明书的完善，请帮我确认：
1. 当前规格是否遗漏了重要的功能点？
2. 非功能需求的描述是否充分？
3. 约束条件和假设是否明确？
4. 验收标准是否具体可衡量？
5. 规格的表达是否清晰无歧义？
```

### 需求验证类问题

```
在需求验证方面，请指导：
1. 如何验证需求的正确性？
2. 如何确保需求的完整性？
3. 如何检查需求间的一致性？
4. 如何评估需求的可行性？
5. 如何设计有效的验证方法？
```

### 测试设计类问题

```
关于基于规格的测试设计，请帮助：
1. 如何从规格中提取测试用例？
2. 如何设计边界值和异常测试？
3. 如何验证非功能需求？
4. 如何设计集成测试策略？
5. 如何建立测试覆盖率标准？
```

### 质量保证类问题

```
在规格质量保证方面，请建议：
1. 如何建立规格审查流程？
2. 如何确保规格的可维护性？
3. 如何管理规格的变更？
4. 如何建立规格与代码的追溯？
5. 如何评估规格的质量？
```

## 🚀 任务优化建议

### 基于模块5特点的优化方向

1. **强化规格思维**
   - 培养先写规格再写代码的习惯
   - 重视需求分析和设计阶段
   - 建立质量意识和标准

2. **掌握规格技术**
   - 学习规格说明书的标准格式
   - 掌握需求分析的方法和工具
   - 熟悉验证和测试技术

3. **建立规格文化**
   - 重视文档和规范
   - 建立团队协作标准
   - 培养持续改进意识

### 规格管理策略

1. **规格生命周期管理**
   - 规格的创建和评审
   - 规格的维护和更新
   - 规格的版本控制

2. **质量控制机制**
   - 规格质量标准
   - 审查和验证流程
   - 持续改进机制

3. **工具和方法支持**
   - 规格管理工具
   - 自动化验证方法
   - 协作平台支持

## 📝 使用示例

### 示例1：电商系统规格澄清

**原始规格**：
"用户可以购买商品"

**澄清后的规格**：
```
功能需求：用户商品购买功能
描述：注册用户可以通过系统购买商品并完成支付

具体要求：
1. 用户必须登录后才能购买
2. 商品必须有库存才能购买
3. 支持多种支付方式（支付宝、微信、银行卡）
4. 购买成功后发送确认邮件
5. 库存实时更新

验收标准：
- 购买流程在3步内完成
- 支付响应时间<5秒
- 库存更新准确率100%
- 邮件发送成功率>99%

异常处理：
- 库存不足时提示用户
- 支付失败时保留购物车
- 网络异常时支持重试
```

### 示例2：API接口规格验证

**需求**：用户登录API接口

**验证结果**：
- **正确性**：符合OAuth 2.0标准，安全性要求明确
- **完整性**：包含请求格式、响应格式、错误码定义
- **一致性**：与其他API接口风格统一
- **可测试性**：可设计自动化测试用例

**改进建议**：
- 补充速率限制说明
- 增加安全头部要求
- 明确Token过期处理
- 添加监控指标定义

## 🎯 预期效果

通过使用本澄清助手，学习者应该能够：

1. **创建高质量规格**：编写清晰、完整、可测试的规格说明书
2. **验证规格正确性**：系统性地验证需求的正确性和完整性
3. **设计测试策略**：基于规格设计全面的测试方案
4. **建立质量标准**：制定和执行规格质量标准
5. **实现规格驱动开发**：建立从规格到实现的完整流程

---

*💡 提示：好的规格说明书是成功项目的基础。通过系统性的规格分析和验证，你可以大大提高项目的成功率和质量。*
