// 竞品分析服务
// 用于分析竞争对手的产品、价格、营销策略等信息

export interface CompetitorData {
  id: string;
  name: string;
  url: string;
  products: ProductInfo[];
  pricing: PricingInfo;
  marketing: MarketingInfo;
  lastUpdated: Date;
  dataQuality: DataQualityScore;
}

export interface ProductInfo {
  name: string;
  description: string;
  price: number;
  currency: string;
  availability: 'in_stock' | 'out_of_stock' | 'limited';
  features: string[];
  images: string[];
  reviews: ReviewSummary;
}

export interface PricingInfo {
  strategy: 'premium' | 'competitive' | 'penetration' | 'skimming';
  priceRange: { min: number; max: number; average: number };
  discounts: DiscountInfo[];
  priceHistory: PricePoint[];
}

export interface MarketingInfo {
  channels: string[];
  messaging: string[];
  promotions: PromotionInfo[];
  contentStrategy: ContentStrategy;
}

export interface DataQualityScore {
  completeness: number; // 0-1
  accuracy: number; // 0-1
  freshness: number; // 0-1
  consistency: number; // 0-1
  overall: number; // 0-1
}

export interface AnalysisReport {
  summary: string;
  insights: Insight[];
  recommendations: Recommendation[];
  threats: Threat[];
  opportunities: Opportunity[];
  confidence: number;
  generatedAt: Date;
}

export class CompetitorAnalyzer {
  private aiService: any;
  private dataCollector: DataCollector;
  private qualityValidator: DataQualityValidator;
  private cache = new Map<string, CachedAnalysis>();

  constructor(aiService: any) {
    this.aiService = aiService;
    this.dataCollector = new DataCollector();
    this.qualityValidator = new DataQualityValidator();
  }

  // 主要分析方法
  async analyzeCompetitor(
    competitorUrl: string,
    analysisType: 'full' | 'pricing' | 'products' | 'marketing' = 'full'
  ): Promise<AnalysisReport> {
    
    try {
      // 1. 检查缓存
      const cached = this.getCachedAnalysis(competitorUrl, analysisType);
      if (cached && !this.isCacheExpired(cached)) {
        return cached.report;
      }

      // 2. 收集数据
      const rawData = await this.dataCollector.collectCompetitorData(competitorUrl);
      
      // 3. 验证数据质量
      const qualityScore = await this.qualityValidator.validateData(rawData);
      if (qualityScore.overall < 0.6) {
        throw new Error(`Data quality too low: ${qualityScore.overall}`);
      }

      // 4. 清洗和标准化数据
      const cleanedData = await this.cleanAndNormalizeData(rawData);

      // 5. 执行AI分析
      const analysis = await this.performAIAnalysis(cleanedData, analysisType);

      // 6. 验证分析结果
      const validatedAnalysis = await this.validateAnalysisResults(analysis);

      // 7. 缓存结果
      this.cacheAnalysis(competitorUrl, analysisType, validatedAnalysis);

      return validatedAnalysis;

    } catch (error) {
      console.error('Competitor analysis failed:', error);
      return this.generateFallbackAnalysis(competitorUrl, analysisType);
    }
  }

  // 批量分析多个竞争对手
  async analyzeBatch(
    competitorUrls: string[],
    analysisType: 'full' | 'pricing' | 'products' | 'marketing' = 'full'
  ): Promise<AnalysisReport[]> {
    
    const batchSize = 3; // 限制并发数量
    const results: AnalysisReport[] = [];

    for (let i = 0; i < competitorUrls.length; i += batchSize) {
      const batch = competitorUrls.slice(i, i + batchSize);
      
      const batchPromises = batch.map(url => 
        this.analyzeCompetitor(url, analysisType).catch(error => {
          console.error(`Failed to analyze ${url}:`, error);
          return this.generateFallbackAnalysis(url, analysisType);
        })
      );

      const batchResults = await Promise.all(batchPromises);
      results.push(...batchResults);

      // 在批次之间添加延迟，避免过度请求
      if (i + batchSize < competitorUrls.length) {
        await this.sleep(2000);
      }
    }

    return results;
  }

  // 数据清洗和标准化
  private async cleanAndNormalizeData(rawData: any): Promise<CompetitorData> {
    const cleaned: CompetitorData = {
      id: this.generateId(rawData.url),
      name: this.extractCompanyName(rawData.url),
      url: rawData.url,
      products: await this.normalizeProducts(rawData.products || []),
      pricing: await this.normalizePricing(rawData.pricing || {}),
      marketing: await this.normalizeMarketing(rawData.marketing || {}),
      lastUpdated: new Date(),
      dataQuality: rawData.qualityScore
    };

    return cleaned;
  }

  // 产品信息标准化
  private async normalizeProducts(products: any[]): Promise<ProductInfo[]> {
    return products.map(product => ({
      name: this.cleanText(product.name || ''),
      description: this.cleanText(product.description || ''),
      price: this.normalizePrice(product.price),
      currency: product.currency || 'CNY',
      availability: this.normalizeAvailability(product.availability),
      features: this.extractFeatures(product.features || product.description || ''),
      images: this.normalizeImages(product.images || []),
      reviews: this.normalizeReviews(product.reviews || {})
    }));
  }

  // 价格信息标准化
  private async normalizePricing(pricing: any): Promise<PricingInfo> {
    const prices = pricing.prices || [];
    const priceValues = prices.map(p => this.normalizePrice(p.value)).filter(p => p > 0);

    return {
      strategy: await this.detectPricingStrategy(pricing),
      priceRange: {
        min: Math.min(...priceValues) || 0,
        max: Math.max(...priceValues) || 0,
        average: priceValues.reduce((a, b) => a + b, 0) / priceValues.length || 0
      },
      discounts: this.normalizeDiscounts(pricing.discounts || []),
      priceHistory: this.normalizePriceHistory(pricing.history || [])
    };
  }

  // AI分析执行
  private async performAIAnalysis(
    data: CompetitorData,
    analysisType: string
  ): Promise<AnalysisReport> {
    
    const analysisPrompts = {
      full: this.buildFullAnalysisPrompt(data),
      pricing: this.buildPricingAnalysisPrompt(data),
      products: this.buildProductAnalysisPrompt(data),
      marketing: this.buildMarketingAnalysisPrompt(data)
    };

    const prompt = analysisPrompts[analysisType] || analysisPrompts.full;
    
    // 使用多个模型进行交叉验证
    const models = ['gpt-4', 'claude-3'];
    const results = await Promise.allSettled(
      models.map(model => this.analyzeWithModel(model, prompt))
    );

    const validResults = results
      .filter(result => result.status === 'fulfilled')
      .map(result => (result as PromiseFulfilledResult<any>).value);

    if (validResults.length === 0) {
      throw new Error('All AI models failed to analyze data');
    }

    // 合并多个模型的结果
    return this.mergeAnalysisResults(validResults);
  }

  // 构建完整分析提示词
  private buildFullAnalysisPrompt(data: CompetitorData): string {
    return `
作为专业的竞品分析师，请对以下竞争对手进行全面分析：

竞争对手信息：
公司名称：${data.name}
网站：${data.url}
数据质量：${data.dataQuality.overall}

产品信息：
${this.formatProductsForPrompt(data.products)}

定价策略：
${this.formatPricingForPrompt(data.pricing)}

营销信息：
${this.formatMarketingForPrompt(data.marketing)}

分析要求：
1. 总体竞争力评估
2. 产品优势和劣势分析
3. 定价策略分析
4. 营销策略评估
5. 威胁和机会识别
6. 具体的应对建议

输出格式：
{
  "summary": "简要总结（100字以内）",
  "insights": [
    {
      "category": "产品|定价|营销|战略",
      "title": "洞察标题",
      "description": "详细描述",
      "impact": "high|medium|low",
      "confidence": 0.8
    }
  ],
  "recommendations": [
    {
      "title": "建议标题",
      "description": "具体建议",
      "priority": "high|medium|low",
      "effort": "high|medium|low",
      "impact": "high|medium|low"
    }
  ],
  "threats": [
    {
      "title": "威胁标题",
      "description": "威胁描述",
      "severity": "high|medium|low",
      "probability": 0.7
    }
  ],
  "opportunities": [
    {
      "title": "机会标题", 
      "description": "机会描述",
      "potential": "high|medium|low",
      "timeframe": "short|medium|long"
    }
  ],
  "confidence": 0.85
}

请确保分析客观、数据驱动，避免主观臆断。
`;
  }

  // 构建定价分析提示词
  private buildPricingAnalysisPrompt(data: CompetitorData): string {
    return `
作为定价策略专家，请分析以下竞争对手的定价策略：

竞争对手：${data.name}
定价数据：
${this.formatPricingForPrompt(data.pricing)}

产品信息：
${this.formatProductsForPrompt(data.products)}

分析维度：
1. 定价策略类型识别
2. 价格竞争力分析
3. 价格弹性评估
4. 促销策略分析
5. 定价机会识别

请提供结构化的定价分析报告，包括具体的定价建议。
`;
  }

  // 使用特定模型进行分析
  private async analyzeWithModel(model: string, prompt: string): Promise<any> {
    try {
      const response = await this.aiService.generate(prompt, { model });
      return JSON.parse(response);
    } catch (error) {
      console.error(`Analysis failed with model ${model}:`, error);
      throw error;
    }
  }

  // 合并多个分析结果
  private mergeAnalysisResults(results: any[]): AnalysisReport {
    if (results.length === 1) {
      return {
        ...results[0],
        generatedAt: new Date()
      };
    }

    // 合并多个结果的逻辑
    const merged = {
      summary: this.selectBestSummary(results),
      insights: this.mergeInsights(results),
      recommendations: this.mergeRecommendations(results),
      threats: this.mergeThreats(results),
      opportunities: this.mergeOpportunities(results),
      confidence: this.calculateAverageConfidence(results),
      generatedAt: new Date()
    };

    return merged;
  }

  // 验证分析结果
  private async validateAnalysisResults(analysis: AnalysisReport): Promise<AnalysisReport> {
    // 检查必需字段
    if (!analysis.summary || !analysis.insights || !analysis.recommendations) {
      throw new Error('Analysis result is incomplete');
    }

    // 检查置信度
    if (analysis.confidence < 0.5) {
      console.warn('Analysis confidence is low:', analysis.confidence);
    }

    // 验证洞察的质量
    const validInsights = analysis.insights.filter(insight => 
      insight.title && insight.description && insight.confidence > 0.3
    );

    if (validInsights.length < analysis.insights.length * 0.7) {
      console.warn('Many insights have low quality');
    }

    return {
      ...analysis,
      insights: validInsights
    };
  }

  // 生成降级分析报告
  private generateFallbackAnalysis(url: string, analysisType: string): AnalysisReport {
    return {
      summary: `无法完成对 ${url} 的${analysisType}分析，请稍后重试或联系技术支持。`,
      insights: [{
        category: 'system',
        title: '分析失败',
        description: '由于技术原因，无法完成竞品分析',
        impact: 'high',
        confidence: 0.1
      }],
      recommendations: [{
        title: '重新尝试分析',
        description: '建议稍后重新进行竞品分析，或使用手动分析方法',
        priority: 'high',
        effort: 'low',
        impact: 'medium'
      }],
      threats: [],
      opportunities: [],
      confidence: 0.1,
      generatedAt: new Date()
    };
  }

  // 缓存管理
  private getCachedAnalysis(url: string, type: string): CachedAnalysis | null {
    const key = `${url}_${type}`;
    return this.cache.get(key) || null;
  }

  private cacheAnalysis(url: string, type: string, report: AnalysisReport): void {
    const key = `${url}_${type}`;
    this.cache.set(key, {
      report,
      timestamp: Date.now(),
      ttl: 24 * 60 * 60 * 1000 // 24小时
    });
  }

  private isCacheExpired(cached: CachedAnalysis): boolean {
    return Date.now() - cached.timestamp > cached.ttl;
  }

  // 工具方法
  private generateId(url: string): string {
    return Buffer.from(url).toString('base64').slice(0, 16);
  }

  private extractCompanyName(url: string): string {
    try {
      const domain = new URL(url).hostname;
      return domain.replace(/^www\./, '').split('.')[0];
    } catch {
      return 'Unknown';
    }
  }

  private cleanText(text: string): string {
    return text.trim().replace(/\s+/g, ' ').slice(0, 1000);
  }

  private normalizePrice(price: any): number {
    if (typeof price === 'number') return price;
    if (typeof price === 'string') {
      const match = price.match(/[\d,]+\.?\d*/);
      return match ? parseFloat(match[0].replace(/,/g, '')) : 0;
    }
    return 0;
  }

  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// 辅助接口
interface CachedAnalysis {
  report: AnalysisReport;
  timestamp: number;
  ttl: number;
}

interface Insight {
  category: string;
  title: string;
  description: string;
  impact: 'high' | 'medium' | 'low';
  confidence: number;
}

interface Recommendation {
  title: string;
  description: string;
  priority: 'high' | 'medium' | 'low';
  effort: 'high' | 'medium' | 'low';
  impact: 'high' | 'medium' | 'low';
}

interface Threat {
  title: string;
  description: string;
  severity: 'high' | 'medium' | 'low';
  probability: number;
}

interface Opportunity {
  title: string;
  description: string;
  potential: 'high' | 'medium' | 'low';
  timeframe: 'short' | 'medium' | 'long';
}
