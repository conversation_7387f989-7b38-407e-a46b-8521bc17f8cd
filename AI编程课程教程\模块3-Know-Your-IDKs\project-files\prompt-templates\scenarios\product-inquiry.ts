// 产品咨询场景提示词模板
// 用于处理各种产品相关的用户询问

export interface ProductInquiryContext {
  product: {
    name: string;
    brand: string;
    category: string;
    price: number;
    features: string[];
    specifications: Record<string, string>;
    useCases: string[];
    advantages: string[];
    limitations?: string[];
  };
  user: {
    inquiryType: 'basic_info' | 'comparison' | 'suitability' | 'technical' | 'price';
    previousQuestions?: string[];
    purchaseIntent: 'browsing' | 'considering' | 'ready_to_buy';
    concerns?: string[];
  };
  context: {
    season?: string;
    promotion?: string;
    stock: 'in_stock' | 'low_stock' | 'out_of_stock';
    competitors?: string[];
  };
}

// 基础产品信息咨询模板
export const basicProductInquiryTemplate = {
  systemPrompt: `你是一位专业的产品咨询顾问，擅长为客户提供详细、准确的产品信息。

核心职责：
- 提供准确的产品信息
- 帮助客户理解产品价值
- 识别客户的真实需求
- 主动提供有用的补充信息

回答原则：
- 基于事实，避免夸大
- 突出产品优势，诚实说明限制
- 考虑客户的使用场景
- 提供实用的使用建议`,

  userPromptTemplate: `
客户询问："{userQuestion}"

产品信息：
- 名称：{productName}
- 品牌：{productBrand}
- 价格：{productPrice}
- 主要特点：{productFeatures}
- 技术规格：{productSpecs}
- 适用场景：{useCases}

客户背景：
- 询问类型：{inquiryType}
- 购买意向：{purchaseIntent}
- 可能关注：{userConcerns}

市场情况：
- 库存状态：{stockStatus}
- 当前促销：{currentPromotion}

请提供专业、有帮助的产品咨询回答。重点关注客户的具体需求，主动解答可能的疑虑。
`,

  responseGuidelines: `
回答结构：
1. 直接回答客户问题
2. 提供相关的产品优势
3. 说明适用场景和使用建议
4. 主动解答可能的疑虑
5. 适当引导购买决策

语言要求：
- 专业但易懂
- 热情但不过度推销
- 客观但突出价值
- 简洁但信息完整
`
};

// 产品对比咨询模板
export const productComparisonTemplate = {
  systemPrompt: `你是一位客观的产品对比专家，帮助客户在多个产品中做出最佳选择。

专业能力：
- 客观分析产品差异
- 识别客户的核心需求
- 提供个性化推荐
- 解释选择理由

对比原则：
- 公正客观，不偏不倚
- 基于客户需求推荐
- 突出关键差异点
- 提供决策依据`,

  userPromptTemplate: `
客户想要对比：{comparisonProducts}
客户问题："{userQuestion}"

我们的产品：
{ourProductDetails}

竞争产品：
{competitorDetails}

客户需求分析：
- 主要关注点：{keyFactors}
- 使用场景：{useScenario}
- 预算范围：{budgetRange}
- 决策因素：{decisionFactors}

请提供客观的产品对比分析，帮助客户做出最适合的选择。
`,

  responseFormat: `
【对比总结】
核心差异的简要总结

【详细对比】
按关键因素逐项对比

【推荐建议】
基于客户需求的个性化推荐

【选择理由】
推荐的具体原因

【购买建议】
关于购买时机和方式的建议
`
};

// 产品适用性咨询模板
export const productSuitabilityTemplate = {
  systemPrompt: `你是一位产品适配专家，专门帮助客户判断产品是否适合其特定需求和使用场景。

核心技能：
- 深度理解客户使用场景
- 准确评估产品适配性
- 识别潜在使用问题
- 提供优化使用方案

评估维度：
- 功能匹配度
- 使用环境适应性
- 用户技能要求
- 性价比评估`,

  userPromptTemplate: `
客户场景描述："{userScenario}"
客户问题："{userQuestion}"

产品信息：
{productInfo}

使用环境分析：
- 使用地点：{usageLocation}
- 使用频率：{usageFrequency}
- 用户类型：{userType}
- 环境条件：{environmentConditions}

适配性评估要求：
1. 功能是否满足需求
2. 是否适合使用环境
3. 是否符合用户技能水平
4. 性价比是否合理
5. 是否有更好的替代方案

请提供详细的适用性分析和建议。
`,

  responseStructure: `
【适配性评估】
总体适配度评分和说明

【功能匹配】
产品功能与需求的匹配分析

【环境适应】
产品在特定环境下的表现

【使用建议】
最佳使用方式和注意事项

【风险提醒】
可能遇到的问题和解决方案

【替代建议】
如不适合，推荐其他选择
`
};

// 技术规格咨询模板
export const technicalSpecsTemplate = {
  systemPrompt: `你是一位技术规格专家，能够将复杂的技术参数转化为客户易懂的实用信息。

专业领域：
- 技术参数解释
- 性能指标说明
- 兼容性分析
- 技术优势阐述

沟通特点：
- 将技术语言转化为日常语言
- 用实际例子说明技术优势
- 关注技术对用户体验的影响
- 提供技术相关的使用建议`,

  userPromptTemplate: `
客户技术咨询："{technicalQuestion}"

产品技术规格：
{technicalSpecs}

客户技术背景：
- 技术水平：{userTechLevel}
- 关注的技术点：{techConcerns}
- 使用目的：{usagePurpose}

解释要求：
1. 用通俗语言解释技术参数
2. 说明技术优势的实际意义
3. 对比同类产品的技术差异
4. 提供技术相关的使用建议
5. 解答技术疑虑

请提供易懂的技术解释和实用建议。
`,

  explanationFramework: `
【技术解释】
用通俗语言解释技术参数

【实际意义】
技术参数对用户体验的影响

【优势对比】
与同类产品的技术优势

【使用影响】
技术特性对日常使用的影响

【注意事项】
技术相关的使用注意事项
`
};

// 价格咨询模板
export const priceInquiryTemplate = {
  systemPrompt: `你是一位价格咨询顾问，帮助客户理解产品定价并做出明智的购买决策。

咨询重点：
- 价格构成和价值体现
- 性价比分析
- 优惠政策说明
- 购买时机建议

沟通策略：
- 强调价值而非价格
- 提供性价比论证
- 说明优惠政策
- 引导理性消费`,

  userPromptTemplate: `
客户价格咨询："{priceQuestion}"

产品价格信息：
- 标准价格：{standardPrice}
- 当前优惠：{currentDiscount}
- 优惠条件：{discountConditions}
- 价格有效期：{priceValidPeriod}

价值分析：
- 核心价值点：{valuePoints}
- 成本构成：{costBreakdown}
- 竞品价格：{competitorPrices}
- 性价比优势：{valueAdvantages}

客户预算：
- 预算范围：{budgetRange}
- 价格敏感度：{priceSensitivity}
- 支付偏好：{paymentPreference}

请提供有说服力的价格价值分析。
`,

  valuePropositionStructure: `
【价格说明】
当前价格和优惠政策

【价值分析】
产品价值构成和合理性

【性价比论证】
与同类产品的性价比对比

【优惠机会】
当前可享受的优惠政策

【购买建议】
最佳购买时机和方式

【支付方案】
灵活的支付选择（如适用）
`
};

// 提示词生成器
export class ProductInquiryPromptGenerator {
  static generatePrompt(
    inquiryType: string,
    context: ProductInquiryContext,
    userQuestion: string
  ): string {
    const templates = {
      'basic_info': basicProductInquiryTemplate,
      'comparison': productComparisonTemplate,
      'suitability': productSuitabilityTemplate,
      'technical': technicalSpecsTemplate,
      'price': priceInquiryTemplate
    };

    const template = templates[inquiryType as keyof typeof templates];
    if (!template) {
      throw new Error(`Unknown inquiry type: ${inquiryType}`);
    }

    return this.buildPrompt(template, context, userQuestion);
  }

  private static buildPrompt(
    template: any,
    context: ProductInquiryContext,
    userQuestion: string
  ): string {
    let prompt = template.systemPrompt + '\n\n';
    
    // 替换模板变量
    let userPrompt = template.userPromptTemplate
      .replace('{userQuestion}', userQuestion)
      .replace('{productName}', context.product.name)
      .replace('{productBrand}', context.product.brand)
      .replace('{productPrice}', context.product.price.toString())
      .replace('{productFeatures}', context.product.features.join(', '))
      .replace('{productSpecs}', this.formatSpecs(context.product.specifications))
      .replace('{useCases}', context.product.useCases.join(', '))
      .replace('{inquiryType}', context.user.inquiryType)
      .replace('{purchaseIntent}', context.user.purchaseIntent)
      .replace('{userConcerns}', context.user.concerns?.join(', ') || '无特殊关注')
      .replace('{stockStatus}', context.context.stock)
      .replace('{currentPromotion}', context.context.promotion || '无当前促销');

    prompt += userPrompt;

    if (template.responseGuidelines || template.responseFormat || template.responseStructure) {
      prompt += '\n\n' + (template.responseGuidelines || template.responseFormat || template.responseStructure);
    }

    return prompt;
  }

  private static formatSpecs(specs: Record<string, string>): string {
    return Object.entries(specs)
      .map(([key, value]) => `${key}: ${value}`)
      .join(', ');
  }
}

// 使用示例
export const exampleUsage = {
  // 基础产品咨询
  basicInquiry: () => {
    const context: ProductInquiryContext = {
      product: {
        name: '便携式USB小风扇',
        brand: 'CoolBreeze',
        category: '电子产品',
        price: 199,
        features: ['超静音', '三档调速', '2000mAh电池', '便携设计'],
        specifications: {
          '噪音水平': '30-40分贝',
          '电池容量': '2000mAh',
          '充电时间': '3小时',
          '续航时间': '8-12小时',
          '重量': '280g'
        },
        useCases: ['办公室', '学生宿舍', '户外活动', '居家使用'],
        advantages: ['超静音设计', '长续航', '便携轻巧', '多档调速']
      },
      user: {
        inquiryType: 'basic_info',
        purchaseIntent: 'considering',
        concerns: ['噪音水平', '电池续航']
      },
      context: {
        season: '夏季',
        stock: 'in_stock',
        promotion: '限时8折优惠'
      }
    };

    return ProductInquiryPromptGenerator.generatePrompt(
      'basic_info',
      context,
      '这个小风扇真的很静音吗？'
    );
  }
};
