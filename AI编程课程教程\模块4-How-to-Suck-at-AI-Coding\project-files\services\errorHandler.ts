// 错误处理和恢复服务
// 用于处理AI系统中的各种错误情况和异常

export enum ErrorType {
  INPUT_VALIDATION = 'input_validation',
  CONTEXT_BUILDING = 'context_building',
  PROMPT_CONSTRUCTION = 'prompt_construction',
  MODEL_EXECUTION = 'model_execution',
  OUTPUT_PROCESSING = 'output_processing',
  RATE_LIMIT = 'rate_limit',
  NETWORK_ERROR = 'network_error',
  BUSINESS_LOGIC = 'business_logic',
  UNKNOWN = 'unknown'
}

export enum ErrorSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

export interface AIError {
  type: ErrorType;
  severity: ErrorSeverity;
  message: string;
  originalError?: Error;
  context?: any;
  timestamp: Date;
  retryable: boolean;
  suggestedActions: string[];
}

export interface RecoveryStrategy {
  name: string;
  condition: (error: AIError) => boolean;
  execute: (error: AIError, context: any) => Promise<any>;
  maxRetries: number;
  backoffMultiplier: number;
}

export interface ErrorHandlerConfig {
  maxRetries: number;
  baseDelay: number;
  maxDelay: number;
  enableFallback: boolean;
  logErrors: boolean;
  notifyOnCritical: boolean;
}

export class AIErrorHandler {
  private config: ErrorHandlerConfig;
  private recoveryStrategies: RecoveryStrategy[] = [];
  private errorHistory: AIError[] = [];
  private retryAttempts = new Map<string, number>();

  constructor(config: Partial<ErrorHandlerConfig> = {}) {
    this.config = {
      maxRetries: 3,
      baseDelay: 1000,
      maxDelay: 30000,
      enableFallback: true,
      logErrors: true,
      notifyOnCritical: true,
      ...config
    };

    this.initializeDefaultStrategies();
  }

  // 处理错误的主要方法
  async handleError<T>(
    error: Error | AIError,
    context: any,
    operation: () => Promise<T>
  ): Promise<T> {
    const aiError = this.normalizeError(error, context);
    this.recordError(aiError);

    // 检查是否可以重试
    if (aiError.retryable && this.canRetry(aiError, context)) {
      return await this.retryWithBackoff(operation, aiError, context);
    }

    // 尝试恢复策略
    const strategy = this.findRecoveryStrategy(aiError);
    if (strategy) {
      try {
        return await strategy.execute(aiError, context);
      } catch (recoveryError) {
        console.warn('Recovery strategy failed:', recoveryError);
      }
    }

    // 如果启用了降级方案
    if (this.config.enableFallback) {
      return await this.executeFallback(aiError, context);
    }

    // 最终抛出错误
    throw aiError;
  }

  // 标准化错误对象
  private normalizeError(error: Error | AIError, context: any): AIError {
    if (this.isAIError(error)) {
      return error;
    }

    // 根据错误信息分类
    const errorType = this.classifyError(error);
    const severity = this.assessSeverity(error, errorType);

    return {
      type: errorType,
      severity,
      message: error.message,
      originalError: error,
      context,
      timestamp: new Date(),
      retryable: this.isRetryable(errorType, error),
      suggestedActions: this.generateSuggestedActions(errorType, error)
    };
  }

  // 错误分类
  private classifyError(error: Error): ErrorType {
    const message = error.message.toLowerCase();
    const stack = error.stack?.toLowerCase() || '';

    if (message.includes('validation') || message.includes('invalid input')) {
      return ErrorType.INPUT_VALIDATION;
    }
    
    if (message.includes('rate limit') || message.includes('quota exceeded')) {
      return ErrorType.RATE_LIMIT;
    }
    
    if (message.includes('network') || message.includes('timeout') || message.includes('connection')) {
      return ErrorType.NETWORK_ERROR;
    }
    
    if (message.includes('model') || message.includes('ai') || message.includes('generation')) {
      return ErrorType.MODEL_EXECUTION;
    }
    
    if (message.includes('context') || message.includes('session')) {
      return ErrorType.CONTEXT_BUILDING;
    }
    
    if (message.includes('prompt') || message.includes('template')) {
      return ErrorType.PROMPT_CONSTRUCTION;
    }
    
    if (message.includes('output') || message.includes('parsing') || message.includes('format')) {
      return ErrorType.OUTPUT_PROCESSING;
    }
    
    if (message.includes('business') || message.includes('rule') || message.includes('policy')) {
      return ErrorType.BUSINESS_LOGIC;
    }

    return ErrorType.UNKNOWN;
  }

  // 评估错误严重程度
  private assessSeverity(error: Error, type: ErrorType): ErrorSeverity {
    // 关键业务逻辑错误
    if (type === ErrorType.BUSINESS_LOGIC) {
      return ErrorSeverity.HIGH;
    }

    // 输入验证错误通常不严重
    if (type === ErrorType.INPUT_VALIDATION) {
      return ErrorSeverity.LOW;
    }

    // 网络和速率限制错误是中等严重
    if (type === ErrorType.NETWORK_ERROR || type === ErrorType.RATE_LIMIT) {
      return ErrorSeverity.MEDIUM;
    }

    // 模型执行错误可能很严重
    if (type === ErrorType.MODEL_EXECUTION) {
      return ErrorSeverity.HIGH;
    }

    // 未知错误默认为高严重性
    if (type === ErrorType.UNKNOWN) {
      return ErrorSeverity.CRITICAL;
    }

    return ErrorSeverity.MEDIUM;
  }

  // 判断错误是否可重试
  private isRetryable(type: ErrorType, error: Error): boolean {
    const retryableTypes = [
      ErrorType.NETWORK_ERROR,
      ErrorType.RATE_LIMIT,
      ErrorType.MODEL_EXECUTION
    ];

    if (!retryableTypes.includes(type)) {
      return false;
    }

    // 某些特定错误不应重试
    const nonRetryableMessages = [
      'invalid api key',
      'authentication failed',
      'permission denied',
      'malformed request'
    ];

    const message = error.message.toLowerCase();
    return !nonRetryableMessages.some(msg => message.includes(msg));
  }

  // 生成建议的修复操作
  private generateSuggestedActions(type: ErrorType, error: Error): string[] {
    const actions: Record<ErrorType, string[]> = {
      [ErrorType.INPUT_VALIDATION]: [
        '检查输入数据的格式和完整性',
        '验证必需字段是否存在',
        '确保数据类型正确'
      ],
      [ErrorType.CONTEXT_BUILDING]: [
        '刷新用户上下文数据',
        '检查会话是否过期',
        '验证上下文数据的完整性'
      ],
      [ErrorType.PROMPT_CONSTRUCTION]: [
        '检查提示词模板的语法',
        '验证变量替换是否正确',
        '确保提示词长度在限制范围内'
      ],
      [ErrorType.MODEL_EXECUTION]: [
        '检查AI服务的可用性',
        '验证API密钥和权限',
        '尝试使用备用模型'
      ],
      [ErrorType.OUTPUT_PROCESSING]: [
        '检查AI输出的格式',
        '验证解析逻辑',
        '添加输出格式验证'
      ],
      [ErrorType.RATE_LIMIT]: [
        '等待速率限制重置',
        '实现请求队列',
        '考虑升级API计划'
      ],
      [ErrorType.NETWORK_ERROR]: [
        '检查网络连接',
        '验证服务端点可用性',
        '增加请求超时时间'
      ],
      [ErrorType.BUSINESS_LOGIC]: [
        '检查业务规则配置',
        '验证数据是否符合业务要求',
        '联系业务团队确认规则'
      ],
      [ErrorType.UNKNOWN]: [
        '查看详细错误日志',
        '联系技术支持',
        '尝试重新启动服务'
      ]
    };

    return actions[type] || ['查看错误详情并联系技术支持'];
  }

  // 检查是否可以重试
  private canRetry(error: AIError, context: any): boolean {
    const key = this.getRetryKey(error, context);
    const attempts = this.retryAttempts.get(key) || 0;
    return attempts < this.config.maxRetries;
  }

  // 带退避的重试机制
  private async retryWithBackoff<T>(
    operation: () => Promise<T>,
    error: AIError,
    context: any
  ): Promise<T> {
    const key = this.getRetryKey(error, context);
    const attempts = this.retryAttempts.get(key) || 0;
    
    // 计算延迟时间（指数退避）
    const delay = Math.min(
      this.config.baseDelay * Math.pow(2, attempts),
      this.config.maxDelay
    );

    // 等待
    await this.sleep(delay);

    // 增加重试计数
    this.retryAttempts.set(key, attempts + 1);

    try {
      const result = await operation();
      // 成功后清除重试计数
      this.retryAttempts.delete(key);
      return result;
    } catch (retryError) {
      // 递归重试
      return await this.handleError(retryError, context, operation);
    }
  }

  // 查找恢复策略
  private findRecoveryStrategy(error: AIError): RecoveryStrategy | null {
    return this.recoveryStrategies.find(strategy => strategy.condition(error)) || null;
  }

  // 执行降级方案
  private async executeFallback<T>(error: AIError, context: any): Promise<T> {
    console.warn('Executing fallback for error:', error.type);

    // 根据错误类型执行不同的降级策略
    switch (error.type) {
      case ErrorType.MODEL_EXECUTION:
        return await this.modelExecutionFallback(context);
      
      case ErrorType.OUTPUT_PROCESSING:
        return await this.outputProcessingFallback(context);
      
      case ErrorType.CONTEXT_BUILDING:
        return await this.contextBuildingFallback(context);
      
      default:
        return await this.genericFallback(context);
    }
  }

  // 模型执行降级方案
  private async modelExecutionFallback(context: any): Promise<any> {
    // 使用预定义的模板响应
    const templates = {
      product_inquiry: '感谢您的咨询，我们的产品具有优秀的品质和性能。如需详细信息，请联系客服。',
      price_inquiry: '产品价格请以官网显示为准，如有疑问请联系客服获取最新报价。',
      general: '抱歉，我暂时无法处理您的请求。请稍后重试或联系人工客服。'
    };

    const responseType = context.intent || 'general';
    return templates[responseType] || templates.general;
  }

  // 输出处理降级方案
  private async outputProcessingFallback(context: any): Promise<any> {
    // 返回结构化的错误响应
    return {
      success: false,
      message: '系统暂时无法处理您的请求',
      errorCode: 'OUTPUT_PROCESSING_ERROR',
      suggestedActions: [
        '请稍后重试',
        '联系客服获取帮助',
        '检查输入格式是否正确'
      ]
    };
  }

  // 上下文构建降级方案
  private async contextBuildingFallback(context: any): Promise<any> {
    // 使用最小化的上下文
    const minimalContext = {
      userId: context.userId || 'anonymous',
      timestamp: new Date(),
      channel: context.channel || 'web',
      minimal: true
    };

    return minimalContext;
  }

  // 通用降级方案
  private async genericFallback(context: any): Promise<any> {
    return {
      success: false,
      message: '服务暂时不可用，请稍后重试',
      timestamp: new Date(),
      context: context
    };
  }

  // 初始化默认恢复策略
  private initializeDefaultStrategies(): void {
    // 速率限制恢复策略
    this.addRecoveryStrategy({
      name: 'rate_limit_recovery',
      condition: (error) => error.type === ErrorType.RATE_LIMIT,
      execute: async (error, context) => {
        // 等待更长时间后重试
        await this.sleep(60000); // 等待1分钟
        throw new Error('Rate limit recovery - please retry');
      },
      maxRetries: 1,
      backoffMultiplier: 1
    });

    // 网络错误恢复策略
    this.addRecoveryStrategy({
      name: 'network_error_recovery',
      condition: (error) => error.type === ErrorType.NETWORK_ERROR,
      execute: async (error, context) => {
        // 尝试使用备用端点
        if (context.useBackupEndpoint) {
          context.endpoint = context.backupEndpoint;
          throw new Error('Network recovery - retry with backup endpoint');
        }
        throw error;
      },
      maxRetries: 2,
      backoffMultiplier: 2
    });
  }

  // 添加自定义恢复策略
  addRecoveryStrategy(strategy: RecoveryStrategy): void {
    this.recoveryStrategies.push(strategy);
  }

  // 记录错误
  private recordError(error: AIError): void {
    this.errorHistory.push(error);

    // 保持错误历史在合理大小
    if (this.errorHistory.length > 1000) {
      this.errorHistory = this.errorHistory.slice(-500);
    }

    // 记录日志
    if (this.config.logErrors) {
      console.error('AI Error:', {
        type: error.type,
        severity: error.severity,
        message: error.message,
        timestamp: error.timestamp
      });
    }

    // 关键错误通知
    if (this.config.notifyOnCritical && error.severity === ErrorSeverity.CRITICAL) {
      this.notifyCriticalError(error);
    }
  }

  // 关键错误通知
  private notifyCriticalError(error: AIError): void {
    // 这里可以集成邮件、短信、Slack等通知服务
    console.error('CRITICAL AI ERROR:', error);
  }

  // 工具方法
  private isAIError(error: any): error is AIError {
    return error && typeof error === 'object' && 'type' in error && 'severity' in error;
  }

  private getRetryKey(error: AIError, context: any): string {
    return `${error.type}_${context.userId || 'anonymous'}_${context.operation || 'unknown'}`;
  }

  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // 获取错误统计
  getErrorStatistics(timeRange?: { start: Date; end: Date }): any {
    let errors = this.errorHistory;
    
    if (timeRange) {
      errors = errors.filter(error => 
        error.timestamp >= timeRange.start && error.timestamp <= timeRange.end
      );
    }

    const stats = {
      total: errors.length,
      byType: {} as Record<string, number>,
      bySeverity: {} as Record<string, number>,
      retryableCount: 0,
      averagePerHour: 0
    };

    errors.forEach(error => {
      stats.byType[error.type] = (stats.byType[error.type] || 0) + 1;
      stats.bySeverity[error.severity] = (stats.bySeverity[error.severity] || 0) + 1;
      if (error.retryable) stats.retryableCount++;
    });

    if (timeRange) {
      const hours = (timeRange.end.getTime() - timeRange.start.getTime()) / (1000 * 60 * 60);
      stats.averagePerHour = errors.length / hours;
    }

    return stats;
  }
}
