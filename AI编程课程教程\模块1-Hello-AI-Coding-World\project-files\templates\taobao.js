// 淘宝详情页文案模板
const taobaoTemplate = {
    systemPrompt: `你是一位专业的电商文案策划师，擅长写淘宝产品详情页描述。

写作风格要求：
- 突出产品功能和优势
- 包含详细的规格参数
- 强调性价比和实用性
- 字数控制在300-400字
- 结构清晰，便于快速阅读
- 使用说服力强的销售语言
- 突出产品的独特卖点和竞争优势

文案结构：
1. 产品概述（简洁有力的一句话总结）
2. 核心特色（3-5个主要卖点）
3. 适用场景和用户收益
4. 技术规格参数（如适用）
5. 购买理由总结`,

    userPrompt: `请为以下产品写一段淘宝详情页描述：

产品信息：
- 名称：{productName}
- 类别：{productCategory}
- 目标用户：{targetAudience}
- 核心卖点：{keyFeatures}

要求：
1. 开头简洁有力的产品概述，突出核心价值
2. 详细的功能特点介绍，使用bullet points
3. 具体的使用场景和用户收益
4. 包含相关的规格参数（根据产品类别）
5. 最后总结购买理由，强调性价比
6. 语言要专业可信，突出产品优势
7. 使用适当的符号和格式增强可读性

示例结构：
【产品名称】核心价值描述

🌟 产品特色：
✓ 特色1 - 具体描述
✓ 特色2 - 具体描述
✓ 特色3 - 具体描述

🎯 适用场景：
• 场景1 - 收益描述
• 场景2 - 收益描述

📊 产品规格：
- 规格1：参数
- 规格2：参数

💝 选择理由：总结性描述`,

    // 不同类别的规格参数模板
    specTemplates: {
        electronics: [
            '尺寸规格',
            '电池容量',
            '充电接口',
            '材质工艺',
            '重量参数',
            '兼容性'
        ],
        fashion: [
            '面料成分',
            '尺码规格',
            '版型设计',
            '颜色选择',
            '洗护说明',
            '适合季节'
        ],
        beauty: [
            '产品容量',
            '主要成分',
            '适用肌质',
            '使用方法',
            '保质期限',
            '产地信息'
        ],
        home: [
            '产品尺寸',
            '材质说明',
            '承重能力',
            '安装方式',
            '保养方法',
            '包装清单'
        ]
    },

    // 销售语言模板
    salesLanguage: {
        quality: ['精选材质', '匠心工艺', '品质保证', '严格品控'],
        value: ['超值性价比', '物超所值', '经济实惠', '高性价比之选'],
        function: ['实用性强', '功能齐全', '操作简便', '效果显著'],
        service: ['售后无忧', '品质保障', '快速发货', '贴心服务']
    },

    // 场景描述模板
    scenarioTemplates: {
        office: '办公室使用，提升工作效率',
        home: '居家必备，提升生活品质',
        outdoor: '户外活动，随时随地享受便利',
        travel: '旅行伴侣，轻松便携',
        gift: '送礼佳品，实用贴心',
        daily: '日常使用，方便实用'
    }
};

// 导出模板
if (typeof module !== 'undefined' && module.exports) {
    module.exports = taobaoTemplate;
}
