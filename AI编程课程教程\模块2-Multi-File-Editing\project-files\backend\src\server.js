// 小红书内容管理系统后端服务器
const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const compression = require('compression');
const rateLimit = require('express-rate-limit');
const { createServer } = require('http');
const { Server } = require('socket.io');
require('dotenv').config();

// 导入路由
const authRoutes = require('./routes/auth');
const userRoutes = require('./routes/users');
const contentRoutes = require('./routes/content');
const templateRoutes = require('./routes/templates');
const analyticsRoutes = require('./routes/analytics');
const planRoutes = require('./routes/plans');

// 导入中间件
const errorHandler = require('./middleware/errorHandler');
const authMiddleware = require('./middleware/auth');
const logger = require('./utils/logger');

// 导入数据库
const { sequelize } = require('./models');

// 创建Express应用
const app = express();
const server = createServer(app);

// 创建Socket.IO实例
const io = new Server(server, {
  cors: {
    origin: process.env.FRONTEND_URL || "http://localhost:3000",
    methods: ["GET", "POST"]
  }
});

// 基础中间件
app.use(helmet()); // 安全头
app.use(compression()); // 响应压缩
app.use(cors({
  origin: process.env.FRONTEND_URL || "http://localhost:3000",
  credentials: true
}));

// 请求日志
app.use(morgan('combined', {
  stream: {
    write: (message) => logger.info(message.trim())
  }
}));

// 请求解析
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// 速率限制
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 100, // 限制每个IP 15分钟内最多100个请求
  message: {
    error: 'Too many requests from this IP, please try again later.'
  }
});
app.use('/api/', limiter);

// API路由
app.use('/api/auth', authRoutes);
app.use('/api/users', authMiddleware, userRoutes);
app.use('/api/content', authMiddleware, contentRoutes);
app.use('/api/templates', authMiddleware, templateRoutes);
app.use('/api/analytics', authMiddleware, analyticsRoutes);
app.use('/api/plans', authMiddleware, planRoutes);

// 健康检查端点
app.get('/health', (req, res) => {
  res.json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: process.env.NODE_ENV || 'development'
  });
});

// API文档端点
app.get('/api', (req, res) => {
  res.json({
    name: 'XiaoHongShu CMS API',
    version: '1.0.0',
    description: '小红书内容管理系统API',
    endpoints: {
      auth: '/api/auth',
      users: '/api/users',
      content: '/api/content',
      templates: '/api/templates',
      analytics: '/api/analytics',
      plans: '/api/plans'
    },
    documentation: '/api/docs'
  });
});

// Socket.IO 连接处理
io.on('connection', (socket) => {
  logger.info(`User connected: ${socket.id}`);

  // 加入内容编辑房间
  socket.on('join-content', (contentId) => {
    socket.join(`content-${contentId}`);
    socket.to(`content-${contentId}`).emit('user-joined', {
      socketId: socket.id,
      timestamp: new Date()
    });
    logger.info(`User ${socket.id} joined content ${contentId}`);
  });

  // 离开内容编辑房间
  socket.on('leave-content', (contentId) => {
    socket.leave(`content-${contentId}`);
    socket.to(`content-${contentId}`).emit('user-left', {
      socketId: socket.id,
      timestamp: new Date()
    });
    logger.info(`User ${socket.id} left content ${contentId}`);
  });

  // 内容变更广播
  socket.on('content-change', (data) => {
    const { contentId, change } = data;
    socket.to(`content-${contentId}`).emit('content-changed', {
      change,
      author: socket.id,
      timestamp: new Date()
    });
  });

  // 光标位置同步
  socket.on('cursor-position', (data) => {
    const { contentId, position } = data;
    socket.to(`content-${contentId}`).emit('cursor-updated', {
      socketId: socket.id,
      position,
      timestamp: new Date()
    });
  });

  // 断开连接处理
  socket.on('disconnect', () => {
    logger.info(`User disconnected: ${socket.id}`);
  });
});

// 404处理
app.use('*', (req, res) => {
  res.status(404).json({
    error: 'Route not found',
    message: `Cannot ${req.method} ${req.originalUrl}`
  });
});

// 错误处理中间件
app.use(errorHandler);

// 数据库连接和服务器启动
const PORT = process.env.PORT || 5000;

async function startServer() {
  try {
    // 测试数据库连接
    await sequelize.authenticate();
    logger.info('Database connection established successfully.');

    // 同步数据库模型（开发环境）
    if (process.env.NODE_ENV === 'development') {
      await sequelize.sync({ alter: true });
      logger.info('Database models synchronized.');
    }

    // 启动服务器
    server.listen(PORT, () => {
      logger.info(`Server is running on port ${PORT}`);
      logger.info(`Environment: ${process.env.NODE_ENV || 'development'}`);
      logger.info(`API Documentation: http://localhost:${PORT}/api`);
    });

  } catch (error) {
    logger.error('Unable to start server:', error);
    process.exit(1);
  }
}

// 优雅关闭处理
process.on('SIGTERM', async () => {
  logger.info('SIGTERM received, shutting down gracefully');
  
  server.close(async () => {
    logger.info('HTTP server closed');
    
    try {
      await sequelize.close();
      logger.info('Database connection closed');
      process.exit(0);
    } catch (error) {
      logger.error('Error during shutdown:', error);
      process.exit(1);
    }
  });
});

process.on('SIGINT', async () => {
  logger.info('SIGINT received, shutting down gracefully');
  
  server.close(async () => {
    logger.info('HTTP server closed');
    
    try {
      await sequelize.close();
      logger.info('Database connection closed');
      process.exit(0);
    } catch (error) {
      logger.error('Error during shutdown:', error);
      process.exit(1);
    }
  });
});

// 未捕获异常处理
process.on('uncaughtException', (error) => {
  logger.error('Uncaught Exception:', error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

// 启动服务器
startServer();

module.exports = { app, server, io };
