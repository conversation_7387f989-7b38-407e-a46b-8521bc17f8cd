/* 电商产品描述生成器样式文件 */

/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
}

/* 头部样式 */
header {
    text-align: center;
    margin-bottom: 40px;
    color: white;
}

header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

header p {
    font-size: 1.2rem;
    opacity: 0.9;
}

/* 主要内容区域 */
main {
    background: white;
    border-radius: 15px;
    padding: 40px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

/* 表单样式 */
#productForm {
    margin-bottom: 30px;
}

.form-group {
    margin-bottom: 25px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #555;
    font-size: 1.1rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-group textarea {
    min-height: 100px;
    resize: vertical;
}

/* 按钮样式 */
button {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 15px 30px;
    border-radius: 8px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    width: 100%;
    margin-bottom: 10px;
}

button:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

button:active {
    transform: translateY(0);
}

/* 结果区域样式 */
.result-container {
    margin-top: 30px;
    padding: 25px;
    background: #f8f9fa;
    border-radius: 10px;
    border-left: 5px solid #667eea;
}

.result-container h3 {
    color: #333;
    margin-bottom: 20px;
    font-size: 1.3rem;
}

#generatedContent {
    background: white;
    padding: 20px;
    border-radius: 8px;
    border: 1px solid #e1e5e9;
    margin-bottom: 20px;
    min-height: 150px;
}

#generatedContent pre {
    white-space: pre-wrap;
    word-wrap: break-word;
    font-family: inherit;
    line-height: 1.6;
    margin: 0;
}

/* 加载和错误状态 */
.loading {
    text-align: center;
    color: #667eea;
    font-size: 1.1rem;
    padding: 40px;
}

.error {
    text-align: center;
    color: #dc3545;
    font-size: 1.1rem;
    padding: 40px;
}

/* 操作按钮组 */
#regenerate,
#copy {
    width: 48%;
    display: inline-block;
    margin-right: 2%;
    margin-bottom: 0;
}

#copy {
    margin-right: 0;
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
}

#copy:hover {
    box-shadow: 0 5px 15px rgba(40, 167, 69, 0.4);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    main {
        padding: 20px;
    }
    
    header h1 {
        font-size: 2rem;
    }
    
    #regenerate,
    #copy {
        width: 100%;
        margin-right: 0;
        margin-bottom: 10px;
    }
}

/* 动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.result-container {
    animation: fadeIn 0.5s ease;
}

/* 提示文本样式 */
::placeholder {
    color: #999;
    font-style: italic;
}

/* 选择框样式优化 */
select {
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 12px center;
    background-size: 20px;
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
}

/* 成功状态样式 */
.success {
    color: #28a745;
    font-weight: 600;
}

/* 工具提示样式 */
.tooltip {
    position: relative;
    display: inline-block;
}

.tooltip .tooltiptext {
    visibility: hidden;
    width: 200px;
    background-color: #555;
    color: #fff;
    text-align: center;
    border-radius: 6px;
    padding: 5px 10px;
    position: absolute;
    z-index: 1;
    bottom: 125%;
    left: 50%;
    margin-left: -100px;
    opacity: 0;
    transition: opacity 0.3s;
    font-size: 0.9rem;
}

.tooltip:hover .tooltiptext {
    visibility: visible;
    opacity: 1;
}
