
今天我们探讨 Windsurf，这是一款强大的代码编辑器，它利用 AI 帮助您显著提升编码效率。它不仅是开启 AI 编码之旅的理想起点，也能很好地处理大型代码库。因此，无论您的经验水平和项目规模如何，它都是一款在 AI 辅助编码时值得使用的优秀工具。

它与其他 AI 编码工具有许多相似之处，但也具备一些使其与众不同的独特功能。今天，我将展示其所有核心特性，并从零开始构建一个基础的全栈应用程序。您可以访问 windsurf.com 下载并开始使用这款编辑器。

Windsurf 是一款在您计算机本地运行的应用程序，而非像某些其他 AI 编码工具那样在浏览器中运行。关于价格，它提供免费版本，但功能相对有限，主要用于体验该工具。之后，您可能需要升级到专业版计划（Pro Plan），我发现这是一个性价比非常高的 AI 编码解决方案。

编辑器界面概览

下载并打开 Windsurf 后，您会看到类似这样的界面。如果您曾使用过 VS Code 或其他编码工具，可以跳过此部分。我将介绍所有面板，以便初学者了解其布局：

左侧：文件浏览器 (File Explorer)
这部分用于查看您计算机上文件夹中的文件。例如，打开一个名为“博客演示”的文件夹后，您可以在此看到该文件夹内的所有文件。

中间：代码查看器 (Code Viewer)
当您在文件浏览器中选择一个文件（例如 posts.ts），其内容（代码）会显示在此区域。顶部有类似标签页的快速导航，方便在不同文件间切换。

右侧：Cascade 面板 (Cascade Panel)
这是 Windsurf 内部主要的 AI 工具。您可以将其理解为一个连接到您整个代码库的 ChatGPT。它能够回答关于您代码的问题、创建新文件，甚至修改现有文件并直接将更改插入其中。当然，它的功能远不止于此，我们稍后会深入探讨所有细节。

底部（可选）：终端 (Terminal)
通过点击顶部菜单栏的“终端” -> “新建终端”，可以在底部打开一个新的面板。这允许您在计算机上运行命令，例如安装特定软件或启动您的应用程序。

您也可以从 Cascade 聊天窗口运行某些命令，我稍后会演示。总而言之，文件浏览器、中间的代码查看器和右侧的 Cascade 面板是 Windsurf 最核心的组成部分。底部可选的终端窗口也是一个常用功能。

从零开始构建全栈应用

现在，让我们开始一个新项目，展示如何使用 Windsurf 非常快速地从零构建一个全栈应用程序。我将通过“文件” -> “打开文件夹”打开一个名为 Windsurf demo 3 的空文件夹。

第一步是思考软件的设计。我将进入 Cascade 面板，切换到聊天模式 (Chat Mode)，然后选择我想使用的 AI 模型。关于 Windsurf 需要了解的是，它集成了来自 Google、OpenAI 和 Anthropic 等公司的顶尖模型，并且会随着新模型的推出而持续更新。

因此，您始终可以使用最新、最强大的模型。在录制本视频时，我个人认为最有用的两个模型是 Claude 3.5 Sonnet 和 Gemini 1.5 Pro (原文为 Gemini 2.5 Pro，根据当前可用模型信息修正)。我发现 Sonnet 非常擅长精确执行指令，而 Gemini 1.5 Pro 在考虑替代设计和辅助架构解决方案方面表现出色。

Claude 3 Opus 也是一个选项，但我个人感觉它有时会过度设计并产生不必要的代码，因此较少使用。近期也新增了 GPT-4o (原文为 GPT4.0) 和 Llama 3，我尚未有机会充分体验这些模型。

让我们选择 Gemini 1.5 Pro。我将描述我想构建的软件类型，并从中获取一个高层架构。我将使用语音转文本功能，这是我电脑自带的听写功能，无需额外软件（Mac 上按两次 Fn 键，Windows 上按 Win + H）。

与 AI 沟通需求

“我想构建一个应用程序来帮助我检查域名的可用性。我希望能够提供一个名称，并使用 WHOIS API 同时检查多个顶级域名 (TLD)。你能帮我选择合适的技术并给我一个架构的概览吗？”

提交后，AI 给出了一些关于语言和库的建议。我个人倾向于全部使用 JavaScript，因为它同时适用于前端和后端。AI 最初似乎建议创建一个命令行工具，但我希望构建一个带有 React 前端的全栈应用，所以我将进一步说明：

“我想使用 React 并将其打造成一个全栈 JavaScript 应用程序。你会如何组织代码，在这种情况下你会使用哪些库？”

AI 回答说我们可以使用 create-react-app 或 Vite，并建议使用 Node.js 和 Express，同时给出了项目结构的概览。

这展示了您如何在编辑器中与 AI 进行直接对话。现在，我们将把这个计划付诸实践，让 Windsurf 为我们实现项目的初稿。

AI 辅助项目初始化

“我们来创建一个项目，其中包含一个 ui 文件夹（用于存放 Vite React 项目）和一个 server 文件夹（用于存放我们的 Node.js 后端）。我们可以使用 whois 包来检查域名，并使用 Express API。”

同时，我将切换到写入模式 (Write Mode)，这将允许 Windsurf 直接为我创建文件。

Windsurf 开始工作，并迅速创建了几个将作为我们服务器的文件。

代码编写完成后，它会提供一些需要运行的命令，我们可以直接从 Cascade 聊天窗口运行。例如，安装必要的依赖项以确保代码正常运行。我将运行这些命令。

接下来，它开始处理 UI 部分，运行命令创建 Vite React 项目。如果命令需要确认，可以点击“在终端中打开”按钮，在终端中输入确认（例如 Y）。很快，一堆文件（如 app.jsx 和各种配置文件）就被创建了。我们还需要在 ui 文件夹内运行 npm install。

UI 设置完成后，我们可以运行 npm run dev 来启动服务器和 UI。

处理启动问题与内置预览

如果 UI 没有按预期加载，AI 可能会建议运行额外的命令。执行后，我们现在有两个服务在运行：UI 和服务器，分别在不同的终端窗口中。

Windsurf 的一个独特功能是内置预览。您可以点击“打开预览”按钮，直接在编码工具中查看网站的运行效果，无需单独打开浏览器。更酷的是，在尝试修改界面时，您可以选择界面上的特定元素作为上下文提供给 Windsurf。我们稍后会尝试这个功能。

目前，我们有了一个基本的 UI 界面。我尝试输入一个域名（如 windsurf）并点击“检查可用性”。在服务器的日志中，可以看到它正在进行检查，并期望它能响应到前端。

虽然初始的响应可能不太易读，但至少 UI 和服务器之间的通信是正常的。我们稍后会改进 UI。

使用新聊天会话处理新功能

接下来，我将打开一个新的聊天会话。请注意，这样做通常会关闭当前的预览服务器。如果您想在新聊天中继续与预览交互，需要让 AI 重新启动预览。我倾向于在开发新功能时开启新的对话，因为每次发送消息时，完整的聊天历史都会作为附加上下文发送给 AI。过多的上下文有时会分散 AI 的注意力，影响其性能。一个集中的提示和有限的上下文能让 AI 更准确地执行您的指令。因此，为每个新功能开启新聊天是一个好习惯。

改进 UI 外观

我将要求 AI：“请使用 Tailwind CSS 使 UI 看起来更好。” 我会选择 Sonnet 3.5，因为它通常更擅长这类专注的任务。

值得注意的是，我并没有标记任何文件，Windsurf 自动识别出需要修改 app.jsx 文件。这是 Windsurf 强大的上下文管理能力之一：它能找出与请求相关的文件，读取它们，然后进行修改。

AI 可能会进行一些额外的搜索，并建议运行安装命令。如果安装过程中遇到错误，Windsurf 可能会自动识别并尝试手动创建配置文件。

AI 可能会建议移除 index.css 的大部分内容，这通常是引入 Tailwind 后的预期行为。然后它会修改 app.jsx。

审查 AI 的更改

打开修改后的文件，您可以看到具体的更改：移除的内容会以红色高亮显示，添加的内容以黄绿色高亮显示。这有助于理解 AI 的操作，判断是否符合预期。您可以逐个接受更改，但我通常会接受一个文件的所有更改，如果出现问题则回滚，或尝试迭代改进代码。

默认情况下，这些更改已应用到文件中。因此，当我运行 npm run dev 时，将使用更新后的文件版本。

处理 Tailwind CSS 版本问题

查看预览时，可能会遇到错误。例如，Tailwind CSS 版本 4 的安装方式可能与 AI 的认知有所不同。此时，可以在聊天中标记额外的上下文。我将标记 @Tailwind CSS docs，并告知 AI 安装未成功，粘贴错误信息，并请求它参考文档正确安装 Tailwind CSS 版本 4。

Windsurf 会搜索这些 Web 资源，并利用这些附加上下文来修改代码。它可能会建议卸载旧包并安装新包。

如果问题依然存在，可以直接将错误信息粘贴到聊天中，看 AI 是否能解决。如果不行，可以回滚到之前的步骤。例如，向上滚动到之前使用新 Tailwind 文档的步骤，并选择“恢复到此步骤”，这将撤销之后的所有代码更改。这种回滚到先前检查点的能力是 AI 辅助编码的强大之处。

在这种情况下，我将要求它改用 Tailwind CSS 第三版。

重启服务与测试

更改为 Tailwind v3 后，UI 应该能正常显示。再次测试域名检查功能。如果遇到“获取失败”的错误，可能是因为开启新聊天时服务器已停止。此时，需要打开一个新的终端，cd 进入 server 目录，并运行 npm run dev 启动服务器。

重新测试后，UI 应该看起来更美观了。

改进服务器代码的健壮性

我们可以进一步改进。例如，选中服务器日志中的超时错误信息，通过按钮将其添加到聊天中，让 Windsurf 利用这些日志内容。然后要求它：“请改进我们的服务器代码，使其对超时错误和其他可能的错误情况更有弹性。”

AI 会进行代码更改。打开相关文件查看，Windsurf 的一个优点是它直接将更改应用到文件中，无需像使用 ChatGPT 那样手动复制代码并整合。我将信任这些更改并接受。

编辑器提供了一个工具栏，可以方便地在文件间导航并查看更改。了解代码的宏观行为非常重要，这样才能在代码库增长时有效地提示 AI。

我将接受所有文件的所有更改，并再次尝试预览。刷新页面，再次检查域名。现在，我们可以看到哪些域名可用，哪些不可用。

添加 AI 生成域名建议功能

接下来，我想添加一个核心功能：使用 AI 根据我的商业想法描述来生成域名建议。

我将开始一个新的聊天，并标记 @web，以便它搜索网络，因为我想在应用中使用最新的 Gemini API。标记 @web 会像之前标记 Tailwind 文档一样，让 AI 查找外部资源作为上下文来正确修改文件。

当请求需要代码库之外的上下文时，标记 @web 非常有用。您也可以标记代码库中的特定文件以集中 AI 的注意力，这在大型代码库或复杂文件夹结构中尤其有帮助。

现在，使用 @web 上下文，我将提出请求：“我想添加一个功能，让我输入我的商业想法，并使用 Gemini API（例如 Gemini 1.5 Pro）来生成域名。你能将此功能完整地添加到全栈中吗？”

这种请求的范围我认为是与 AI 协作的最佳平衡点：它不是小到只修改一个文件或函数，也不是大到试图一次性构建整个产品。它是一个会影响多个文件的全栈功能，但又足够集中，AI 能够很好地完成。

处理 API 密钥与配置文件

代码编写完成后，AI 会提示将 Gemini API 密钥放入一个 .env 文件中。Windsurf 会遵循 .gitignore 文件，这意味着我们可以阻止敏感信息（如 API 密钥）被发送到 Windsurf 服务器。创建 .env 文件并放入 API 密钥后，Windsurf 会忽略其内容。

另一个特殊文件是 windsurf.rules。创建此文件（例如 windsurf.rules），可以定义 Windsurf 在每个请求中都会遵循的指令。我个人不常用这类规则文件，因为我没有需要在每个 AI 请求中都包含的通用指令。但它在特定场景下可能很有用，例如进行测试驱动开发，或者有特定的技术栈或方法论希望 AI 始终遵守。

规则文件中可以包含测试框架、不希望修改的文件、不希望使用的 API 等。我通常倾向于将这些信息包含在具体的提示中，而不是使用规则文件为每个请求定义。有一个包含各种 windsurf.rules 示例的目录，感兴趣可以查看。

添加 API 密钥到 .env 文件后，查看 UI 的变化。打开预览，现在应该有了一个生成域名建议的功能。

调试 AI 生成的代码

尝试使用默认的商业想法（例如“手工皂在线市场”）生成域名建议。如果遇到错误，查看服务器代码。可以将错误信息直接复制粘贴到聊天中，看 AI 是否能修复。

这是故障排除的常见流程，不要期望 AI 第一次就能写出完美的代码。它生成的代码通常是一个好的起点，但可能会遇到错误，需要让 AI 修复或回滚。这是 AI 辅助编码的常态。

如果再次请求后仍有错误，仔细查看错误信息。例如，模型名称可能不正确。Windsurf 可能会自动建议正确的模型名称，您可以按 Tab 键接受。或者，您可以手动指定一个确认可用的模型，例如 Gemini 1.5 Pro preview。

使用 Windsurf Tab 快速修改代码

修复错误后，再次尝试，应该能看到域名建议了。点击建议，可以将其填充到域名输入框中。如果想实现点击按钮检查所有建议域名的功能，可以查看相关代码。

首先，接受所有之前的更改，以便更清晰地查看代码。然后找到负责将建议添加到域名搜索框的点击事件处理代码。例如，setDomainName(suggestion)。如果希望将多个建议用逗号分隔拼接起来，可以修改这部分逻辑。

在输入代码时，Windsurf 会尝试预测您想写的内容并给出建议。例如，输入 if (domainName)，它可能会自动补全后续的拼接逻辑。您可以根据需要调整，例如添加逗号分隔符，或者处理没有初始域名的情况。这种预测和自动补全能显著提高编码效率。

修改代码后，回到预览界面测试。点击多个建议，它们应该会按预期添加到输入框中。然后运行检查，确认端到端功能正常。

一键部署应用

Windsurf 还有一个强大的功能：直接从代码编辑器部署应用程序。目前这主要适用于静态前端，但我希望未来能扩展到后端部署。像我们构建的这个应用，理论上可以完全从 Windsurf 部署。

为了演示，我将打开一个空项目，创建一个简单的前端。

“创建一个 Three.js 游戏，类似于太空入侵者，允许我控制一艘宇宙飞船并射击向我飞来的外星人。”

经过一些开发，我们有了一个可运行的游戏。现在，我将部署它。方法很简单，直接要求 Windsurf 部署应用程序。

Windsurf 会分析您的项目，并创建一个 netlify.toml 文件，指定应用程序的部署方式。它在底层使用 Netlify 来托管这些应用。点击部署后，它会构建应用并将其部署到一个类似 SpaceInvaders3Dgame.windsurf.build 的地址。

部署完成后，您就可以通过该链接访问并玩游戏了。整个过程都在 Windsurf 内部完成，无需其他特殊软件。

这对于快速部署游戏和一次性工具非常有用。希望未来他们也能支持服务器部署。

总结与后续学习

以上就是 Windsurf 的核心功能介绍。还有一些未涵盖的特性，例如：

使用图像生成代码的能力。

Windsurf 可以根据您与它的对话创建“记忆”（Memories）。

与 MCP (Multi-Content Prompting) 服务器的集成。 这是一个更高级的主题，允许 AI 代理利用其他工具并查询数据库或其他服务。我有一个关于 MCP 的专门视频，您可以观看。

最重要的下一步是学习如何优化您的提示，以便从 AI 中获得最大收益。请务必观看下一个相关视频。感谢您的观看。