# 模块6：Advanced AI Coding Patterns - 高级AI编程模式

## 📚 学习目标

完成本模块后，您将能够：

- [ ] 掌握高级AI编程设计模式和架构模式
- [ ] 学会构建可扩展、可维护的AI驱动系统
- [ ] 掌握AI系统的性能优化和资源管理技术
- [ ] 学会设计AI系统的安全和隐私保护机制
- [ ] 完成一个企业级AI助手平台项目

**预期学习时间**：3周（每周20小时）
**实践项目**：企业级AI助手平台

---

## 🧠 第一性原理解析：系统设计的基本原则

### 从最基本的组织原理开始

观察自然界中的高效系统：

**蜂巢的设计**：
- 六边形结构：最节省材料，最大化空间
- 分工明确：工蜂、兵蜂、蜂王各司其职
- 通信机制：通过舞蹈传递信息
- 自我修复：损坏部分可以重建

**人体的设计**：
- 模块化：心脏、肺、肝脏等独立器官
- 层次化：细胞→组织→器官→系统
- 冗余备份：两个肾脏、两个肺叶
- 反馈调节：体温、血糖自动调节

**关键洞察**：**高效的系统都遵循相同的设计原则**。

### 系统设计的五个基本原则

**1. 模块化原则**（分而治之）
- 复杂问题分解为简单问题
- 每个模块专注一个功能
- 模块之间相对独立

**2. 层次化原则**（分层管理）
- 不同层次处理不同抽象级别
- 上层依赖下层，下层服务上层
- 每层隐藏下层的复杂性

**3. 复用原则**（避免重复）
- 相同的功能只实现一次
- 通过参数化适应不同场景
- 建立可重用的组件库

**4. 抽象原则**（隐藏细节）
- 只暴露必要的接口
- 隐藏内部实现细节
- 降低系统复杂度

**5. 演化原则**（适应变化）
- 系统可以逐步改进
- 新功能容易添加
- 旧功能容易修改

### 用城市规划来理解系统设计

**糟糕的城市**（没有规划）：
- 道路杂乱无章
- 功能区混杂
- 交通拥堵
- 扩展困难

**优秀的城市**（有规划）：
- 道路网络清晰
- 功能区分明确（住宅区、商业区、工业区）
- 交通系统高效
- 可以有序扩展

**软件系统设计的对应关系**：
- 道路网络 = 数据流和控制流
- 功能区 = 不同的模块和组件
- 交通系统 = 模块间的通信机制
- 城市扩展 = 系统的可扩展性

### 从简单模式到复杂模式的演进

**第一阶段：基础模式**（解决单一问题）
- 就像学会使用锤子、螺丝刀等基本工具
- 每个模式解决一个特定问题
- 容易理解和应用

**第二阶段：组合模式**（解决复合问题）
- 就像学会使用工具箱
- 多个基础模式组合使用
- 解决更复杂的问题

**第三阶段：架构模式**（解决系统问题）
- 就像学会设计整个工厂
- 从全局角度设计系统
- 考虑性能、安全、可维护性

### 模式的本质：经验的结晶

**为什么需要模式？**

想象你要学做菜：

**方式A：每次从零开始**
- 每道菜都要摸索
- 重复犯同样的错误
- 进步很慢

**方式B：学习经典菜谱**
- 站在前人的肩膀上
- 避免已知的陷阱
- 快速掌握技巧

**编程模式就是"经典菜谱"**：
- 前人总结的最佳实践
- 经过验证的解决方案
- 可以直接使用或改进

### AI时代的模式特殊性

**传统编程模式**：
- 主要考虑代码的组织和复用
- 假设逻辑是确定的
- 重点是效率和可维护性

**AI编程模式**：
- 需要处理不确定性
- 需要考虑AI的特殊性质
- 重点是可靠性和可解释性

**新的挑战**：
1. **不确定性处理**：AI输出可能不稳定
2. **上下文管理**：AI需要丰富的上下文信息
3. **质量控制**：AI输出需要验证和过滤
4. **性能优化**：AI调用成本较高
5. **安全考虑**：AI可能产生有害内容

### 从原理到实践的推理链条

**第一步：理解复杂性的本质**
- 复杂系统不是简单系统的放大
- 需要新的组织原则和设计模式
- 必须考虑系统的整体性质

**第二步：识别重复出现的问题**
- 在AI编程中，某些问题会反复出现
- 这些问题有相似的解决思路
- 可以抽象出通用的解决模式

**第三步：建立模式库**
- 收集和整理成功的解决方案
- 抽象出可重用的设计模式
- 建立模式之间的关系

**第四步：指导实际开发**
- 根据问题特征选择合适的模式
- 根据具体情况调整模式
- 组合多个模式解决复杂问题

### 通俗理解：模式就像乐高积木

**基础积木**（基础模式）：
- 每个积木有特定的形状和功能
- 可以单独使用
- 是构建复杂结构的基础

**组合结构**（组合模式）：
- 多个积木组合成更大的结构
- 实现单个积木无法实现的功能
- 仍然保持模块化特性

**完整作品**（架构模式）：
- 整体设计和规划
- 考虑美观、稳定、功能
- 是艺术和工程的结合

**AI编程模式的价值**：
- 提供标准化的"积木"
- 加速开发过程
- 保证质量和可靠性
- 便于团队协作

---

## 🎯 理论基础：高级AI编程模式

### 什么是AI编程模式？

**AI编程模式**是在AI驱动的软件开发中反复出现的设计问题的可重用解决方案。这些模式帮助开发者：

1. **提高代码质量**：通过经过验证的设计模式
2. **加速开发**：重用成熟的解决方案
3. **降低风险**：避免常见的设计陷阱
4. **提升可维护性**：建立清晰的架构结构

### AI编程模式的分类

#### 1. 创建型模式（Creational Patterns）
- **AI Model Factory**：动态创建和配置AI模型
- **Prompt Builder**：构建复杂的提示词
- **Context Manager**：管理AI上下文状态

#### 2. 结构型模式（Structural Patterns）
- **AI Adapter**：适配不同的AI服务接口
- **AI Proxy**：代理和缓存AI服务调用
- **AI Composite**：组合多个AI能力

#### 3. 行为型模式（Behavioral Patterns）
- **AI Chain of Responsibility**：AI处理链
- **AI Strategy**：动态选择AI策略
- **AI Observer**：监控AI系统状态

#### 4. 架构型模式（Architectural Patterns）
- **AI Gateway**：统一AI服务入口
- **AI Pipeline**：AI处理流水线
- **AI Event Sourcing**：AI事件溯源

### 高级AI编程模式理论深度解析

#### 设计模式理论基础

**模式语言的构成要素**：

```mermaid
graph TD
    A[设计模式语言] --> B[模式名称<br/>Pattern Name]
    A --> C[问题描述<br/>Problem Description]
    A --> D[解决方案<br/>Solution]
    A --> E[效果分析<br/>Consequences]

    B --> B1[简洁明确的标识]
    B --> B2[便于交流和记忆]
    B --> B3[体现模式本质]

    C --> C1[问题上下文<br/>Problem Context]
    C --> C2[设计约束<br/>Design Constraints]
    C --> C3[质量要求<br/>Quality Requirements]

    D --> D1[结构设计<br/>Structure Design]
    D --> D2[行为定义<br/>Behavior Definition]
    D --> D3[实现指导<br/>Implementation Guidance]

    E --> E1[优势分析<br/>Benefits Analysis]
    E --> E2[劣势评估<br/>Drawbacks Assessment]
    E --> E3[适用场景<br/>Applicable Scenarios]

    style A fill:#e1f5fe
    style B fill:#fff3e0
    style C fill:#f3e5f5
    style D fill:#e8f5e8
    style E fill:#fce4ec
```

**AI编程模式的特殊性**：

| 传统设计模式 | AI编程模式 | 主要差异 |
|-------------|------------|----------|
| 确定性逻辑 | 概率性推理 | 需要处理不确定性和错误 |
| 静态结构 | 动态适应 | 模式需要支持学习和进化 |
| 本地计算 | 远程服务 | 需要考虑网络延迟和故障 |
| 同步处理 | 异步交互 | 大量异步和并发处理 |
| 精确控制 | 近似优化 | 追求效果而非精确性 |

#### 企业级架构模式理论

**分层架构在AI系统中的演进**：

```mermaid
graph TB
    subgraph "传统分层架构"
        A1[表示层] --> A2[业务逻辑层]
        A2 --> A3[数据访问层]
        A3 --> A4[数据存储层]
    end

    subgraph "AI增强分层架构"
        B1[智能交互层<br/>Intelligent Interaction Layer]
        B2[AI服务层<br/>AI Service Layer]
        B3[业务逻辑层<br/>Business Logic Layer]
        B4[数据访问层<br/>Data Access Layer]
        B5[数据存储层<br/>Data Storage Layer]
        B6[模型管理层<br/>Model Management Layer]

        B1 --> B2
        B2 --> B3
        B3 --> B4
        B4 --> B5
        B2 --> B6
        B6 --> B5
    end

    subgraph "云原生AI架构"
        C1[API网关层<br/>API Gateway Layer]
        C2[AI编排层<br/>AI Orchestration Layer]
        C3[微服务层<br/>Microservices Layer]
        C4[数据平台层<br/>Data Platform Layer]
        C5[基础设施层<br/>Infrastructure Layer]

        C1 --> C2
        C2 --> C3
        C3 --> C4
        C4 --> C5
    end

    style A1 fill:#e3f2fd
    style B1 fill:#f3e5f5
    style C1 fill:#e8f5e8
```

**微服务架构在AI系统中的应用**：

```mermaid
graph LR
    A[AI微服务架构] --> B[服务拆分策略<br/>Service Decomposition]
    A --> C[服务通信模式<br/>Communication Patterns]
    A --> D[数据管理策略<br/>Data Management]
    A --> E[服务治理机制<br/>Service Governance]

    B --> B1[按AI能力拆分<br/>By AI Capability]
    B --> B2[按业务域拆分<br/>By Business Domain]
    B --> B3[按数据类型拆分<br/>By Data Type]
    B --> B4[按性能要求拆分<br/>By Performance Requirements]

    C --> C1[同步调用<br/>Synchronous Calls]
    C --> C2[异步消息<br/>Asynchronous Messaging]
    C --> C3[事件驱动<br/>Event-Driven]
    C --> C4[流式处理<br/>Stream Processing]

    D --> D1[数据库分离<br/>Database Separation]
    D --> D2[数据同步<br/>Data Synchronization]
    D --> D3[数据一致性<br/>Data Consistency]
    D --> D4[数据版本管理<br/>Data Versioning]

    E --> E1[服务发现<br/>Service Discovery]
    E --> E2[负载均衡<br/>Load Balancing]
    E --> E3[熔断降级<br/>Circuit Breaking]
    E --> E4[监控告警<br/>Monitoring & Alerting]

    style A fill:#e1f5fe
    style B fill:#fff3e0
    style C fill:#f3e5f5
    style D fill:#e8f5e8
    style E fill:#fce4ec
```

#### 并发与异步处理模式

**AI系统的并发模型**：

```mermaid
sequenceDiagram
    participant C as 客户端
    participant G as API网关
    participant O as 编排器
    participant A1 as AI服务1
    participant A2 as AI服务2
    participant A3 as AI服务3
    participant D as 数据服务

    C->>G: 1. 复杂AI请求
    G->>O: 2. 路由到编排器

    par 并行处理
        O->>A1: 3a. 文本分析
        O->>A2: 3b. 情感分析
        O->>A3: 3c. 实体识别
    end

    A1->>D: 4a. 查询历史数据
    A2->>D: 4b. 查询用户画像
    A3->>D: 4c. 查询知识库

    D->>A1: 5a. 返回历史数据
    D->>A2: 5b. 返回用户画像
    D->>A3: 5c. 返回知识库数据

    A1->>O: 6a. 文本分析结果
    A2->>O: 6b. 情感分析结果
    A3->>O: 6c. 实体识别结果

    O->>O: 7. 结果聚合和推理
    O->>G: 8. 综合结果
    G->>C: 9. 最终响应

    Note over O,A3: 并行AI处理
    Note over A1,D: 数据访问优化
    Note over O: 智能结果聚合
```

**异步处理模式分类**：

| 模式类型 | 适用场景 | 实现方式 | 优势 | 劣势 |
|----------|----------|----------|------|------|
| 请求-响应 | 实时交互 | HTTP/gRPC | 简单直观 | 阻塞等待 |
| 发布-订阅 | 事件通知 | Message Queue | 解耦性好 | 复杂性高 |
| 任务队列 | 批量处理 | Redis/RabbitMQ | 可靠性高 | 延迟较大 |
| 流式处理 | 实时数据 | Kafka/Pulsar | 吞吐量大 | 状态管理复杂 |
| 回调模式 | 异步通知 | Webhook/Callback | 及时响应 | 错误处理复杂 |

#### 缓存与性能优化理论

**多层缓存架构**：

```mermaid
graph TD
    A[多层缓存架构] --> B[浏览器缓存<br/>Browser Cache]
    A --> C[CDN缓存<br/>CDN Cache]
    A --> D[应用缓存<br/>Application Cache]
    A --> E[AI模型缓存<br/>AI Model Cache]
    A --> F[数据库缓存<br/>Database Cache]

    B --> B1[静态资源缓存]
    B --> B2[API响应缓存]
    B --> B3[用户状态缓存]

    C --> C1[全球分发]
    C --> C2[边缘计算]
    C --> C3[智能路由]

    D --> D1[内存缓存<br/>Memory Cache]
    D --> D2[分布式缓存<br/>Distributed Cache]
    D --> D3[本地缓存<br/>Local Cache]

    E --> E1[模型权重缓存]
    E --> E2[推理结果缓存]
    E --> E3[上下文缓存]

    F --> F1[查询结果缓存]
    F --> F2[连接池缓存]
    F --> F3[预计算缓存]

    style A fill:#e1f5fe
    style B fill:#fff3e0
    style C fill:#f3e5f5
    style D fill:#e8f5e8
    style E fill:#fce4ec
    style F fill:#e0f2f1
```

**AI特定的缓存策略**：

1. **语义缓存（Semantic Caching）**
   - 基于语义相似性的缓存匹配
   - 使用向量相似度计算
   - 适用于自然语言处理任务

2. **上下文感知缓存（Context-Aware Caching）**
   - 考虑上下文信息的缓存策略
   - 动态调整缓存优先级
   - 提高缓存命中率

3. **预测性缓存（Predictive Caching）**
   - 基于用户行为预测的预加载
   - 机器学习驱动的缓存决策
   - 主动缓存热点数据

4. **分层失效策略（Hierarchical Invalidation）**
   - 多层缓存的一致性管理
   - 智能失效传播机制
   - 最小化缓存更新成本

#### 安全与隐私保护模式

**AI系统安全威胁模型**：

```mermaid
graph LR
    A[AI安全威胁] --> B[输入威胁<br/>Input Threats]
    A --> C[模型威胁<br/>Model Threats]
    A --> D[输出威胁<br/>Output Threats]
    A --> E[系统威胁<br/>System Threats]

    B --> B1[对抗样本<br/>Adversarial Examples]
    B --> B2[提示注入<br/>Prompt Injection]
    B --> B3[数据投毒<br/>Data Poisoning]
    B --> B4[输入验证绕过<br/>Input Validation Bypass]

    C --> C1[模型窃取<br/>Model Extraction]
    C --> C2[成员推理<br/>Membership Inference]
    C --> C3[模型反演<br/>Model Inversion]
    C --> C4[后门攻击<br/>Backdoor Attacks]

    D --> D1[信息泄露<br/>Information Leakage]
    D --> D2[有害内容生成<br/>Harmful Content Generation]
    D --> D3[偏见放大<br/>Bias Amplification]
    D --> D4[虚假信息<br/>Misinformation]

    E --> E1[权限提升<br/>Privilege Escalation]
    E --> E2[拒绝服务<br/>Denial of Service]
    E --> E3[侧信道攻击<br/>Side-channel Attacks]
    E --> E4[供应链攻击<br/>Supply Chain Attacks]

    style A fill:#e1f5fe
    style B fill:#ffebee
    style C fill:#fff3e0
    style D fill:#f3e5f5
    style E fill:#e8f5e8
```

**隐私保护技术框架**：

```mermaid
graph TD
    A[隐私保护技术] --> B[数据保护<br/>Data Protection]
    A --> C[计算保护<br/>Computation Protection]
    A --> D[通信保护<br/>Communication Protection]
    A --> E[访问控制<br/>Access Control]

    B --> B1[数据匿名化<br/>Data Anonymization]
    B --> B2[差分隐私<br/>Differential Privacy]
    B --> B3[数据最小化<br/>Data Minimization]
    B --> B4[数据加密<br/>Data Encryption]

    C --> C1[同态加密<br/>Homomorphic Encryption]
    C --> C2[安全多方计算<br/>Secure Multi-party Computation]
    C --> C3[联邦学习<br/>Federated Learning]
    C --> C4[可信执行环境<br/>Trusted Execution Environment]

    D --> D1[端到端加密<br/>End-to-End Encryption]
    D --> D2[传输层安全<br/>Transport Layer Security]
    D --> D3[零知识证明<br/>Zero-Knowledge Proofs]
    D --> D4[安全通道<br/>Secure Channels]

    E --> E1[基于角色的访问控制<br/>RBAC]
    E --> E2[基于属性的访问控制<br/>ABAC]
    E --> E3[动态访问控制<br/>Dynamic Access Control]
    E --> E4[细粒度权限管理<br/>Fine-grained Permissions]

    style A fill:#e1f5fe
    style B fill:#fff3e0
    style C fill:#f3e5f5
    style D fill:#e8f5e8
    style E fill:#fce4ec
```

**学习检查点**：

- [ ] 理解AI编程模式的理论基础和特殊性
- [ ] 掌握企业级架构模式在AI系统中的应用
- [ ] 熟悉并发与异步处理的设计模式
- [ ] 了解多层缓存架构和AI特定缓存策略
- [ ] 掌握AI系统的安全威胁模型和隐私保护技术

**自测题目**：

1. **模式应用题**：设计一个AI客服系统的微服务架构，包括服务拆分策略和通信模式。

2. **性能优化题**：为一个大语言模型应用设计多层缓存策略，包括缓存层次和失效机制。

3. **安全设计题**：分析AI代码生成工具可能面临的安全威胁，并设计相应的防护措施。

4. **架构演进题**：描述从传统分层架构到AI增强架构的演进过程，并分析各阶段的优缺点。

---

## 🏗️ 创建型模式详解

### 1. AI Model Factory Pattern

**问题**：需要根据不同场景动态创建和配置AI模型

**解决方案**：使用工厂模式封装模型创建逻辑

```typescript
// AI模型抽象接口
interface AIModel {
  generate(prompt: string, options?: GenerationOptions): Promise<string>;
  getCapabilities(): ModelCapabilities;
  getMetrics(): ModelMetrics;
}

// 模型配置接口
interface ModelConfig {
  provider: 'openai' | 'anthropic' | 'google' | 'local';
  model: string;
  temperature?: number;
  maxTokens?: number;
  timeout?: number;
  retryPolicy?: RetryPolicy;
}

// AI模型工厂
class AIModelFactory {
  private static modelCache = new Map<string, AIModel>();
  private static configRegistry = new Map<string, ModelConfig>();

  // 注册模型配置
  static registerConfig(name: string, config: ModelConfig): void {
    this.configRegistry.set(name, config);
  }

  // 创建模型实例
  static async createModel(
    configName: string,
    overrides?: Partial<ModelConfig>
  ): Promise<AIModel> {
    
    const cacheKey = `${configName}_${JSON.stringify(overrides)}`;
    
    // 检查缓存
    if (this.modelCache.has(cacheKey)) {
      return this.modelCache.get(cacheKey)!;
    }

    // 获取配置
    const baseConfig = this.configRegistry.get(configName);
    if (!baseConfig) {
      throw new Error(`Model config '${configName}' not found`);
    }

    const finalConfig = { ...baseConfig, ...overrides };
    
    // 根据提供商创建模型
    let model: AIModel;
    switch (finalConfig.provider) {
      case 'openai':
        model = new OpenAIModel(finalConfig);
        break;
      case 'anthropic':
        model = new AnthropicModel(finalConfig);
        break;
      case 'google':
        model = new GoogleModel(finalConfig);
        break;
      case 'local':
        model = new LocalModel(finalConfig);
        break;
      default:
        throw new Error(`Unsupported provider: ${finalConfig.provider}`);
    }

    // 缓存模型实例
    this.modelCache.set(cacheKey, model);
    
    return model;
  }

  // 批量创建模型
  static async createModelEnsemble(
    configs: Array<{ name: string; weight: number }>
  ): Promise<ModelEnsemble> {
    
    const models = await Promise.all(
      configs.map(async config => ({
        model: await this.createModel(config.name),
        weight: config.weight
      }))
    );

    return new ModelEnsemble(models);
  }

  // 清理缓存
  static clearCache(): void {
    this.modelCache.clear();
  }
}

// 使用示例
// 注册模型配置
AIModelFactory.registerConfig('fast-chat', {
  provider: 'openai',
  model: 'gpt-3.5-turbo',
  temperature: 0.7,
  maxTokens: 1000,
  timeout: 5000
});

AIModelFactory.registerConfig('deep-analysis', {
  provider: 'anthropic',
  model: 'claude-3-sonnet',
  temperature: 0.3,
  maxTokens: 4000,
  timeout: 30000
});

// 创建模型实例
const chatModel = await AIModelFactory.createModel('fast-chat');
const analysisModel = await AIModelFactory.createModel('deep-analysis', {
  temperature: 0.1 // 覆盖默认配置
});
```

### 2. Prompt Builder Pattern

**问题**：构建复杂、动态的提示词

**解决方案**：使用建造者模式构建提示词

```typescript
// 提示词组件接口
interface PromptComponent {
  render(context: PromptContext): string;
}

// 提示词上下文
interface PromptContext {
  variables: Record<string, any>;
  userInfo?: UserInfo;
  conversationHistory?: Message[];
  systemState?: SystemState;
}

// 系统角色组件
class SystemRoleComponent implements PromptComponent {
  constructor(
    private role: string,
    private personality?: string,
    private capabilities?: string[]
  ) {}

  render(context: PromptContext): string {
    let prompt = `你是${this.role}`;
    
    if (this.personality) {
      prompt += `，具有${this.personality}的特点`;
    }
    
    if (this.capabilities && this.capabilities.length > 0) {
      prompt += `。你的能力包括：${this.capabilities.join('、')}`;
    }
    
    return prompt + '。';
  }
}

// 上下文组件
class ContextComponent implements PromptComponent {
  constructor(private contextType: 'user' | 'conversation' | 'system') {}

  render(context: PromptContext): string {
    switch (this.contextType) {
      case 'user':
        return context.userInfo 
          ? `用户信息：${JSON.stringify(context.userInfo, null, 2)}`
          : '';
      
      case 'conversation':
        return context.conversationHistory
          ? `对话历史：\n${this.formatConversationHistory(context.conversationHistory)}`
          : '';
      
      case 'system':
        return context.systemState
          ? `系统状态：${JSON.stringify(context.systemState, null, 2)}`
          : '';
      
      default:
        return '';
    }
  }

  private formatConversationHistory(history: Message[]): string {
    return history
      .slice(-5) // 只保留最近5条消息
      .map(msg => `${msg.role}: ${msg.content}`)
      .join('\n');
  }
}

// 任务指令组件
class TaskInstructionComponent implements PromptComponent {
  constructor(
    private task: string,
    private requirements?: string[],
    private constraints?: string[],
    private outputFormat?: string
  ) {}

  render(context: PromptContext): string {
    let prompt = `任务：${this.task}\n`;
    
    if (this.requirements && this.requirements.length > 0) {
      prompt += `要求：\n${this.requirements.map(req => `- ${req}`).join('\n')}\n`;
    }
    
    if (this.constraints && this.constraints.length > 0) {
      prompt += `约束：\n${this.constraints.map(con => `- ${con}`).join('\n')}\n`;
    }
    
    if (this.outputFormat) {
      prompt += `输出格式：${this.outputFormat}\n`;
    }
    
    return prompt;
  }
}

// 变量替换组件
class VariableComponent implements PromptComponent {
  constructor(private template: string) {}

  render(context: PromptContext): string {
    let result = this.template;
    
    // 替换变量
    for (const [key, value] of Object.entries(context.variables)) {
      const placeholder = `{{${key}}}`;
      result = result.replace(new RegExp(placeholder, 'g'), String(value));
    }
    
    return result;
  }
}

// 提示词建造者
class PromptBuilder {
  private components: PromptComponent[] = [];
  private separator: string = '\n\n';

  // 添加系统角色
  addSystemRole(role: string, personality?: string, capabilities?: string[]): this {
    this.components.push(new SystemRoleComponent(role, personality, capabilities));
    return this;
  }

  // 添加上下文
  addContext(type: 'user' | 'conversation' | 'system'): this {
    this.components.push(new ContextComponent(type));
    return this;
  }

  // 添加任务指令
  addTaskInstruction(
    task: string,
    requirements?: string[],
    constraints?: string[],
    outputFormat?: string
  ): this {
    this.components.push(new TaskInstructionComponent(task, requirements, constraints, outputFormat));
    return this;
  }

  // 添加变量模板
  addTemplate(template: string): this {
    this.components.push(new VariableComponent(template));
    return this;
  }

  // 添加自定义组件
  addComponent(component: PromptComponent): this {
    this.components.push(component);
    return this;
  }

  // 设置分隔符
  setSeparator(separator: string): this {
    this.separator = separator;
    return this;
  }

  // 构建最终提示词
  build(context: PromptContext): string {
    const renderedComponents = this.components
      .map(component => component.render(context))
      .filter(content => content.trim().length > 0);
    
    return renderedComponents.join(this.separator);
  }

  // 重置建造者
  reset(): this {
    this.components = [];
    this.separator = '\n\n';
    return this;
  }
}

// 使用示例
const promptBuilder = new PromptBuilder();

const prompt = promptBuilder
  .addSystemRole(
    '专业的客服助手',
    '友好、耐心、专业',
    ['产品咨询', '订单处理', '售后服务']
  )
  .addContext('user')
  .addContext('conversation')
  .addTaskInstruction(
    '根据用户问题提供准确的回答',
    [
      '回答要准确、具体',
      '语气要友好、专业',
      '如果不确定要主动询问'
    ],
    [
      '不能提供个人隐私信息',
      '不能承诺超出权限的事项',
      '回答长度不超过200字'
    ],
    'JSON格式：{"answer": "回答内容", "confidence": 0.9, "needsFollowUp": false}'
  )
  .addTemplate('用户问题：{{userQuestion}}')
  .build({
    variables: { userQuestion: '这个产品有什么颜色？' },
    userInfo: { id: 'user123', level: 'vip' },
    conversationHistory: [
      { role: 'user', content: '你好' },
      { role: 'assistant', content: '您好！有什么可以帮助您的吗？' }
    ]
  });

console.log(prompt);
```

### 3. Context Manager Pattern

**问题**：管理复杂的AI上下文状态

**解决方案**：使用上下文管理器模式

```typescript
// 上下文状态接口
interface ContextState {
  conversationId: string;
  userId: string;
  sessionData: Record<string, any>;
  conversationHistory: Message[];
  userProfile: UserProfile;
  systemState: SystemState;
  metadata: ContextMetadata;
}

// 上下文元数据
interface ContextMetadata {
  createdAt: Date;
  lastUpdated: Date;
  version: number;
  tags: string[];
  ttl?: number; // 生存时间（秒）
}

// 上下文存储接口
interface ContextStorage {
  save(contextId: string, state: ContextState): Promise<void>;
  load(contextId: string): Promise<ContextState | null>;
  delete(contextId: string): Promise<void>;
  exists(contextId: string): Promise<boolean>;
  cleanup(): Promise<void>;
}

// 内存存储实现
class MemoryContextStorage implements ContextStorage {
  private storage = new Map<string, ContextState>();
  private timers = new Map<string, NodeJS.Timeout>();

  async save(contextId: string, state: ContextState): Promise<void> {
    this.storage.set(contextId, { ...state });
    
    // 设置TTL
    if (state.metadata.ttl) {
      this.clearTimer(contextId);
      const timer = setTimeout(() => {
        this.storage.delete(contextId);
        this.timers.delete(contextId);
      }, state.metadata.ttl * 1000);
      this.timers.set(contextId, timer);
    }
  }

  async load(contextId: string): Promise<ContextState | null> {
    return this.storage.get(contextId) || null;
  }

  async delete(contextId: string): Promise<void> {
    this.storage.delete(contextId);
    this.clearTimer(contextId);
  }

  async exists(contextId: string): Promise<boolean> {
    return this.storage.has(contextId);
  }

  async cleanup(): Promise<void> {
    // 清理过期的上下文
    const now = Date.now();
    for (const [contextId, state] of this.storage.entries()) {
      if (state.metadata.ttl) {
        const expiryTime = state.metadata.lastUpdated.getTime() + (state.metadata.ttl * 1000);
        if (now > expiryTime) {
          await this.delete(contextId);
        }
      }
    }
  }

  private clearTimer(contextId: string): void {
    const timer = this.timers.get(contextId);
    if (timer) {
      clearTimeout(timer);
      this.timers.delete(contextId);
    }
  }
}

// 上下文管理器
class ContextManager {
  private storage: ContextStorage;
  private compressionThreshold: number = 50; // 消息数量阈值
  private maxHistoryLength: number = 100;

  constructor(storage: ContextStorage) {
    this.storage = storage;
  }

  // 创建新上下文
  async createContext(
    conversationId: string,
    userId: string,
    initialData?: Partial<ContextState>
  ): Promise<ContextState> {
    
    const now = new Date();
    const context: ContextState = {
      conversationId,
      userId,
      sessionData: {},
      conversationHistory: [],
      userProfile: await this.loadUserProfile(userId),
      systemState: await this.getSystemState(),
      metadata: {
        createdAt: now,
        lastUpdated: now,
        version: 1,
        tags: [],
        ttl: 3600 // 默认1小时
      },
      ...initialData
    };

    await this.storage.save(conversationId, context);
    return context;
  }

  // 获取上下文
  async getContext(conversationId: string): Promise<ContextState | null> {
    return await this.storage.load(conversationId);
  }

  // 更新上下文
  async updateContext(
    conversationId: string,
    updates: Partial<ContextState>
  ): Promise<ContextState> {
    
    const context = await this.getContext(conversationId);
    if (!context) {
      throw new Error(`Context not found: ${conversationId}`);
    }

    const updatedContext: ContextState = {
      ...context,
      ...updates,
      metadata: {
        ...context.metadata,
        lastUpdated: new Date(),
        version: context.metadata.version + 1
      }
    };

    await this.storage.save(conversationId, updatedContext);
    return updatedContext;
  }

  // 添加消息到历史
  async addMessage(
    conversationId: string,
    message: Message
  ): Promise<ContextState> {
    
    const context = await this.getContext(conversationId);
    if (!context) {
      throw new Error(`Context not found: ${conversationId}`);
    }

    // 添加消息
    context.conversationHistory.push({
      ...message,
      timestamp: new Date()
    });

    // 检查是否需要压缩历史
    if (context.conversationHistory.length > this.compressionThreshold) {
      await this.compressHistory(context);
    }

    return await this.updateContext(conversationId, {
      conversationHistory: context.conversationHistory
    });
  }

  // 压缩对话历史
  private async compressHistory(context: ContextState): Promise<void> {
    if (context.conversationHistory.length <= this.maxHistoryLength) {
      return;
    }

    // 保留最近的消息和重要的消息
    const recentMessages = context.conversationHistory.slice(-20);
    const importantMessages = context.conversationHistory
      .slice(0, -20)
      .filter(msg => msg.importance === 'high');

    // 对中间的消息进行摘要
    const middleMessages = context.conversationHistory
      .slice(0, -20)
      .filter(msg => msg.importance !== 'high');

    if (middleMessages.length > 0) {
      const summary = await this.summarizeMessages(middleMessages);
      const summaryMessage: Message = {
        role: 'system',
        content: `[对话摘要] ${summary}`,
        timestamp: new Date(),
        type: 'summary'
      };

      context.conversationHistory = [
        ...importantMessages,
        summaryMessage,
        ...recentMessages
      ];
    }
  }

  // 摘要消息
  private async summarizeMessages(messages: Message[]): Promise<string> {
    // 这里可以调用AI服务进行摘要
    // 简化实现
    return `包含${messages.length}条消息的对话摘要`;
  }

  // 删除上下文
  async deleteContext(conversationId: string): Promise<void> {
    await this.storage.delete(conversationId);
  }

  // 清理过期上下文
  async cleanup(): Promise<void> {
    await this.storage.cleanup();
  }

  // 获取用户画像
  private async loadUserProfile(userId: string): Promise<UserProfile> {
    // 从用户服务加载用户画像
    return {
      id: userId,
      preferences: {},
      history: [],
      demographics: {}
    };
  }

  // 获取系统状态
  private async getSystemState(): Promise<SystemState> {
    return {
      timestamp: new Date(),
      version: '1.0.0',
      features: [],
      configuration: {}
    };
  }
}

// 使用示例
const storage = new MemoryContextStorage();
const contextManager = new ContextManager(storage);

// 创建上下文
const context = await contextManager.createContext(
  'conv_123',
  'user_456',
  { sessionData: { theme: 'dark', language: 'zh-CN' } }
);

// 添加消息
await contextManager.addMessage('conv_123', {
  role: 'user',
  content: '你好，我想了解一下产品信息',
  timestamp: new Date()
});

await contextManager.addMessage('conv_123', {
  role: 'assistant',
  content: '您好！我很乐意为您介绍我们的产品。请问您对哪类产品感兴趣？',
  timestamp: new Date()
});
```

---

## 🔗 结构型模式详解

### 1. AI Adapter Pattern

**问题**：需要统一不同AI服务提供商的接口

**解决方案**：使用适配器模式统一接口

```typescript
// 统一的AI服务接口
interface UnifiedAIService {
  generateText(request: TextGenerationRequest): Promise<TextGenerationResponse>;
  generateImage(request: ImageGenerationRequest): Promise<ImageGenerationResponse>;
  analyzeText(request: TextAnalysisRequest): Promise<TextAnalysisResponse>;
  getUsage(): Promise<UsageInfo>;
}

// 统一的请求和响应格式
interface TextGenerationRequest {
  prompt: string;
  maxTokens?: number;
  temperature?: number;
  model?: string;
  stream?: boolean;
}

interface TextGenerationResponse {
  text: string;
  usage: TokenUsage;
  model: string;
  finishReason: 'completed' | 'length' | 'stop';
  metadata?: Record<string, any>;
}

// OpenAI适配器
class OpenAIAdapter implements UnifiedAIService {
  private client: OpenAI;

  constructor(apiKey: string) {
    this.client = new OpenAI({ apiKey });
  }

  async generateText(request: TextGenerationRequest): Promise<TextGenerationResponse> {
    const response = await this.client.chat.completions.create({
      model: request.model || 'gpt-3.5-turbo',
      messages: [{ role: 'user', content: request.prompt }],
      max_tokens: request.maxTokens,
      temperature: request.temperature,
      stream: request.stream || false
    });

    const choice = response.choices[0];
    return {
      text: choice.message?.content || '',
      usage: {
        promptTokens: response.usage?.prompt_tokens || 0,
        completionTokens: response.usage?.completion_tokens || 0,
        totalTokens: response.usage?.total_tokens || 0
      },
      model: response.model,
      finishReason: this.mapFinishReason(choice.finish_reason),
      metadata: {
        id: response.id,
        created: response.created
      }
    };
  }

  async generateImage(request: ImageGenerationRequest): Promise<ImageGenerationResponse> {
    const response = await this.client.images.generate({
      prompt: request.prompt,
      n: request.count || 1,
      size: request.size || '1024x1024',
      quality: request.quality || 'standard'
    });

    return {
      images: response.data.map(img => ({
        url: img.url!,
        revisedPrompt: img.revised_prompt
      })),
      usage: {
        promptTokens: 0, // OpenAI doesn't provide token usage for images
        completionTokens: 0,
        totalTokens: 0
      },
      model: 'dall-e-3'
    };
  }

  async analyzeText(request: TextAnalysisRequest): Promise<TextAnalysisResponse> {
    // 使用GPT进行文本分析
    const analysisPrompt = `
请分析以下文本：
${request.text}

分析类型：${request.analysisType}
输出格式：JSON

请提供详细的分析结果。
`;

    const response = await this.generateText({
      prompt: analysisPrompt,
      temperature: 0.1
    });

    try {
      const analysis = JSON.parse(response.text);
      return {
        analysis,
        confidence: 0.8, // 估算置信度
        usage: response.usage,
        model: response.model
      };
    } catch (error) {
      throw new Error('Failed to parse analysis result');
    }
  }

  async getUsage(): Promise<UsageInfo> {
    // OpenAI没有直接的使用量API，这里返回模拟数据
    return {
      totalTokens: 0,
      totalRequests: 0,
      totalCost: 0,
      period: 'current_month'
    };
  }

  private mapFinishReason(reason: string | null): 'completed' | 'length' | 'stop' {
    switch (reason) {
      case 'stop': return 'completed';
      case 'length': return 'length';
      default: return 'stop';
    }
  }
}

// AI服务工厂
class AIServiceFactory {
  static createService(provider: string, config: any): UnifiedAIService {
    switch (provider.toLowerCase()) {
      case 'openai':
        return new OpenAIAdapter(config.apiKey);
      case 'anthropic':
        return new AnthropicAdapter(config.apiKey);
      case 'google':
        return new GoogleAdapter(config.apiKey);
      default:
        throw new Error(`Unsupported AI provider: ${provider}`);
    }
  }
}

// 使用示例
const aiService = AIServiceFactory.createService('openai', {
  apiKey: process.env.OPENAI_API_KEY
});

const response = await aiService.generateText({
  prompt: '请介绍一下人工智能的发展历史',
  maxTokens: 500,
  temperature: 0.7
});

console.log(response.text);
```

### 2. AI Proxy Pattern

**问题**：需要为AI服务添加缓存、监控、限流等横切关注点

**解决方案**：使用代理模式包装AI服务

```typescript
// AI代理接口
interface AIServiceProxy extends UnifiedAIService {
  getStats(): ProxyStats;
  clearCache(): Promise<void>;
  setRateLimit(limit: RateLimitConfig): void;
}

// AI代理实现
class AIProxy implements AIServiceProxy {
  private target: UnifiedAIService;
  private cache: Map<string, CacheEntry> = new Map();
  private rateLimiter: RateLimiter;
  private monitor: ServiceMonitor;
  private config: ProxyConfig;

  constructor(target: UnifiedAIService, config: ProxyConfig) {
    this.target = target;
    this.config = config;
    this.rateLimiter = new RateLimiter(config.rateLimit);
    this.monitor = new ServiceMonitor();
  }

  async generateText(request: TextGenerationRequest): Promise<TextGenerationResponse> {
    const startTime = Date.now();

    try {
      // 1. 速率限制检查
      await this.rateLimiter.acquire();

      // 2. 缓存检查
      if (this.config.caching.enabled) {
        const cacheKey = this.generateCacheKey('text', request);
        const cached = this.cache.get(cacheKey);

        if (cached && !this.isCacheExpired(cached)) {
          this.monitor.recordCacheHit();
          return cached.data;
        }
      }

      // 3. 调用目标服务
      const response = await this.target.generateText(request);

      // 4. 缓存响应
      if (this.config.caching.enabled) {
        const cacheKey = this.generateCacheKey('text', request);
        this.cache.set(cacheKey, {
          data: response,
          timestamp: Date.now(),
          ttl: this.config.caching.ttl
        });
      }

      // 5. 记录指标
      this.monitor.recordRequest('generateText', Date.now() - startTime, true);

      return response;

    } catch (error) {
      this.monitor.recordRequest('generateText', Date.now() - startTime, false);
      throw error;
    }
  }

  async generateImage(request: ImageGenerationRequest): Promise<ImageGenerationResponse> {
    // 类似的代理逻辑
    return await this.target.generateImage(request);
  }

  async analyzeText(request: TextAnalysisRequest): Promise<TextAnalysisResponse> {
    // 类似的代理逻辑
    return await this.target.analyzeText(request);
  }

  async getUsage(): Promise<UsageInfo> {
    return await this.target.getUsage();
  }

  getStats(): ProxyStats {
    return {
      cacheHitRate: this.monitor.getCacheHitRate(),
      averageResponseTime: this.monitor.getAverageResponseTime(),
      requestCount: this.monitor.getRequestCount(),
      errorRate: this.monitor.getErrorRate(),
      cacheSize: this.cache.size
    };
  }

  async clearCache(): Promise<void> {
    this.cache.clear();
  }

  setRateLimit(limit: RateLimitConfig): void {
    this.rateLimiter.updateConfig(limit);
  }

  private generateCacheKey(operation: string, request: any): string {
    return `${operation}:${JSON.stringify(request)}`;
  }

  private isCacheExpired(entry: CacheEntry): boolean {
    return Date.now() - entry.timestamp > entry.ttl * 1000;
  }
}

// 使用示例
const originalService = AIServiceFactory.createService('openai', config);
const proxiedService = new AIProxy(originalService, {
  caching: {
    enabled: true,
    ttl: 3600, // 1小时
    maxSize: 1000
  },
  rateLimit: {
    requestsPerMinute: 100,
    burstSize: 10
  },
  monitoring: {
    enabled: true,
    metricsInterval: 60000
  }
});

// 透明的代理调用
const response = await proxiedService.generateText({
  prompt: '解释机器学习的基本概念',
  maxTokens: 500
});

// 获取代理统计信息
const stats = proxiedService.getStats();
console.log('缓存命中率:', stats.cacheHitRate);
console.log('平均响应时间:', stats.averageResponseTime);
```

### 3. AI Composite Pattern

**问题**：需要组合多个AI能力形成复合服务

**解决方案**：使用组合模式构建AI能力树

```typescript
// AI能力组件接口
interface AICapability {
  execute(input: any, context: ExecutionContext): Promise<any>;
  getCapabilities(): CapabilityInfo;
  validate(input: any): ValidationResult;
}

// 叶子能力（具体AI功能）
class TextAnalysisCapability implements AICapability {
  private aiService: UnifiedAIService;

  constructor(aiService: UnifiedAIService) {
    this.aiService = aiService;
  }

  async execute(input: TextAnalysisInput, context: ExecutionContext): Promise<any> {
    const request: TextAnalysisRequest = {
      text: input.text,
      analysisType: input.analysisType
    };

    return await this.aiService.analyzeText(request);
  }

  getCapabilities(): CapabilityInfo {
    return {
      name: 'Text Analysis',
      description: '文本分析能力',
      inputTypes: ['text'],
      outputTypes: ['analysis_result'],
      dependencies: []
    };
  }

  validate(input: any): ValidationResult {
    if (!input.text || typeof input.text !== 'string') {
      return { valid: false, errors: ['Text is required'] };
    }
    return { valid: true, errors: [] };
  }
}

// 组合能力（包含多个子能力）
class DocumentProcessingCapability implements AICapability {
  private children: AICapability[] = [];

  constructor() {
    // 添加子能力
    this.addCapability(new TextExtractionCapability());
    this.addCapability(new TextAnalysisCapability());
    this.addCapability(new SummaryGenerationCapability());
  }

  addCapability(capability: AICapability): void {
    this.children.push(capability);
  }

  removeCapability(capability: AICapability): void {
    const index = this.children.indexOf(capability);
    if (index > -1) {
      this.children.splice(index, 1);
    }
  }

  async execute(input: DocumentInput, context: ExecutionContext): Promise<any> {
    const results: any = {};

    // 按顺序执行子能力
    for (const capability of this.children) {
      const capabilityInfo = capability.getCapabilities();

      try {
        // 根据能力类型准备输入
        const capabilityInput = this.prepareInputForCapability(
          input,
          results,
          capabilityInfo
        );

        // 验证输入
        const validation = capability.validate(capabilityInput);
        if (!validation.valid) {
          throw new Error(`Validation failed: ${validation.errors.join(', ')}`);
        }

        // 执行能力
        const result = await capability.execute(capabilityInput, context);
        results[capabilityInfo.name] = result;

      } catch (error) {
        console.error(`Capability ${capabilityInfo.name} failed:`, error);
        results[capabilityInfo.name] = { error: error.message };
      }
    }

    return {
      document: input.document,
      processing_results: results,
      summary: this.generateProcessingSummary(results)
    };
  }

  getCapabilities(): CapabilityInfo {
    const childCapabilities = this.children.map(child => child.getCapabilities());

    return {
      name: 'Document Processing',
      description: '文档处理复合能力',
      inputTypes: ['document'],
      outputTypes: ['processing_result'],
      dependencies: childCapabilities.map(cap => cap.name)
    };
  }

  validate(input: any): ValidationResult {
    if (!input.document) {
      return { valid: false, errors: ['Document is required'] };
    }

    // 验证所有子能力的输入要求
    const errors: string[] = [];
    for (const capability of this.children) {
      const validation = capability.validate(input);
      if (!validation.valid) {
        errors.push(...validation.errors);
      }
    }

    return { valid: errors.length === 0, errors };
  }

  private prepareInputForCapability(
    originalInput: any,
    previousResults: any,
    capabilityInfo: CapabilityInfo
  ): any {
    // 根据能力类型和之前的结果准备输入
    switch (capabilityInfo.name) {
      case 'Text Extraction':
        return { document: originalInput.document };

      case 'Text Analysis':
        return {
          text: previousResults['Text Extraction']?.text || originalInput.text,
          analysisType: originalInput.analysisType || 'sentiment'
        };

      case 'Summary Generation':
        return {
          text: previousResults['Text Extraction']?.text || originalInput.text,
          analysis: previousResults['Text Analysis']
        };

      default:
        return originalInput;
    }
  }

  private generateProcessingSummary(results: any): string {
    const successCount = Object.values(results).filter(r => !r.error).length;
    const totalCount = Object.keys(results).length;

    return `文档处理完成：${successCount}/${totalCount} 个步骤成功执行`;
  }
}

// 使用示例
const documentProcessor = new DocumentProcessingCapability();

const result = await documentProcessor.execute({
  document: documentContent,
  analysisType: 'comprehensive'
}, {
  userId: 'user123',
  sessionId: 'session456',
  preferences: { language: 'zh-CN' }
});

console.log('文档处理结果:', result);
```

---

## ⚡ 行为型模式详解

### 1. AI Chain of Responsibility Pattern

**问题**：需要按顺序处理AI请求的多个步骤

**解决方案**：使用责任链模式构建处理管道

```typescript
// 处理器接口
interface AIRequestHandler {
  setNext(handler: AIRequestHandler): AIRequestHandler;
  handle(request: AIRequest): Promise<AIRequest>;
}

// 抽象处理器
abstract class AbstractAIHandler implements AIRequestHandler {
  private nextHandler: AIRequestHandler | null = null;

  setNext(handler: AIRequestHandler): AIRequestHandler {
    this.nextHandler = handler;
    return handler;
  }

  async handle(request: AIRequest): Promise<AIRequest> {
    const processedRequest = await this.process(request);

    if (this.nextHandler) {
      return await this.nextHandler.handle(processedRequest);
    }

    return processedRequest;
  }

  protected abstract process(request: AIRequest): Promise<AIRequest>;
}

// 输入验证处理器
class InputValidationHandler extends AbstractAIHandler {
  protected async process(request: AIRequest): Promise<AIRequest> {
    console.log('验证输入...');

    // 验证必需字段
    if (!request.prompt || request.prompt.trim().length === 0) {
      throw new Error('Prompt is required');
    }

    // 验证输入长度
    if (request.prompt.length > 10000) {
      throw new Error('Prompt too long');
    }

    // 验证敏感内容
    if (this.containsSensitiveContent(request.prompt)) {
      throw new Error('Prompt contains sensitive content');
    }

    return request;
  }

  private containsSensitiveContent(text: string): boolean {
    const sensitivePatterns = [
      /个人身份证/,
      /银行卡号/,
      /密码/,
      /手机号码/
    ];

    return sensitivePatterns.some(pattern => pattern.test(text));
  }
}

// 上下文增强处理器
class ContextEnrichmentHandler extends AbstractAIHandler {
  private contextManager: ContextManager;

  constructor(contextManager: ContextManager) {
    super();
    this.contextManager = contextManager;
  }

  protected async process(request: AIRequest): Promise<AIRequest> {
    console.log('增强上下文...');

    // 获取用户上下文
    const userContext = await this.contextManager.getUserContext(request.userId);

    // 获取对话历史
    const conversationHistory = await this.contextManager.getConversationHistory(
      request.sessionId
    );

    // 增强请求
    const enrichedRequest = {
      ...request,
      context: {
        ...request.context,
        userProfile: userContext.userProfile,
        conversationHistory: conversationHistory.slice(-10), // 最近10条
        timestamp: new Date(),
        enriched: true
      }
    };

    return enrichedRequest;
  }
}

// AI处理器
class AIProcessingHandler extends AbstractAIHandler {
  private aiService: UnifiedAIService;

  constructor(aiService: UnifiedAIService) {
    super();
    this.aiService = aiService;
  }

  protected async process(request: AIRequest): Promise<AIRequest> {
    console.log('AI处理中...');

    // 构建完整的提示词
    const fullPrompt = this.buildFullPrompt(request);

    // 调用AI服务
    const response = await this.aiService.generateText({
      prompt: fullPrompt,
      maxTokens: request.maxTokens || 1000,
      temperature: request.temperature || 0.7
    });

    // 添加响应到请求对象
    const processedRequest = {
      ...request,
      response: {
        text: response.text,
        usage: response.usage,
        model: response.model,
        finishReason: response.finishReason,
        metadata: response.metadata
      }
    };

    return processedRequest;
  }

  private buildFullPrompt(request: AIRequest): string {
    let prompt = '';

    // 添加系统上下文
    if (request.context?.userProfile) {
      prompt += `用户信息：${JSON.stringify(request.context.userProfile)}\n\n`;
    }

    // 添加对话历史
    if (request.context?.conversationHistory) {
      prompt += '对话历史：\n';
      request.context.conversationHistory.forEach(msg => {
        prompt += `${msg.role}: ${msg.content}\n`;
      });
      prompt += '\n';
    }

    // 添加主要提示词
    prompt += request.prompt;

    return prompt;
  }
}

// 输出后处理器
class OutputPostProcessingHandler extends AbstractAIHandler {
  protected async process(request: AIRequest): Promise<AIRequest> {
    console.log('后处理输出...');

    if (!request.response) {
      throw new Error('No response to post-process');
    }

    // 清理输出
    let cleanedText = request.response.text
      .trim()
      .replace(/\n{3,}/g, '\n\n') // 移除多余的换行
      .replace(/\s{2,}/g, ' '); // 移除多余的空格

    // 内容安全检查
    if (this.containsInappropriateContent(cleanedText)) {
      cleanedText = '[内容已过滤]';
    }

    // 更新响应
    const processedRequest = {
      ...request,
      response: {
        ...request.response,
        text: cleanedText,
        postProcessed: true
      }
    };

    return processedRequest;
  }

  private containsInappropriateContent(text: string): boolean {
    const inappropriatePatterns = [
      /暴力/,
      /仇恨/,
      /歧视/
    ];

    return inappropriatePatterns.some(pattern => pattern.test(text));
  }
}

// 审计日志处理器
class AuditLoggingHandler extends AbstractAIHandler {
  private auditLogger: AuditLogger;

  constructor(auditLogger: AuditLogger) {
    super();
    this.auditLogger = auditLogger;
  }

  protected async process(request: AIRequest): Promise<AIRequest> {
    console.log('记录审计日志...');

    await this.auditLogger.log({
      timestamp: new Date(),
      userId: request.userId,
      sessionId: request.sessionId,
      prompt: request.prompt,
      response: request.response?.text,
      model: request.response?.model,
      tokenUsage: request.response?.usage,
      success: !!request.response
    });

    return request;
  }
}

// 处理链构建器
class AIProcessingChainBuilder {
  private firstHandler: AIRequestHandler | null = null;
  private lastHandler: AIRequestHandler | null = null;

  addHandler(handler: AIRequestHandler): this {
    if (!this.firstHandler) {
      this.firstHandler = handler;
      this.lastHandler = handler;
    } else {
      this.lastHandler!.setNext(handler);
      this.lastHandler = handler;
    }
    return this;
  }

  build(): AIRequestHandler {
    if (!this.firstHandler) {
      throw new Error('No handlers added to chain');
    }
    return this.firstHandler;
  }
}

// 使用示例
const contextManager = new ContextManager();
const aiService = AIServiceFactory.createService('openai', config);
const auditLogger = new AuditLogger();

// 构建处理链
const processingChain = new AIProcessingChainBuilder()
  .addHandler(new InputValidationHandler())
  .addHandler(new ContextEnrichmentHandler(contextManager))
  .addHandler(new AIProcessingHandler(aiService))
  .addHandler(new OutputPostProcessingHandler())
  .addHandler(new AuditLoggingHandler(auditLogger))
  .build();

// 处理请求
const request: AIRequest = {
  userId: 'user123',
  sessionId: 'session456',
  prompt: '请帮我分析一下这个市场报告',
  maxTokens: 1000,
  temperature: 0.7
};

const result = await processingChain.handle(request);
console.log('处理结果:', result.response?.text);
```

### 2. AI Strategy Pattern

**问题**：需要根据不同场景动态选择AI处理策略

**解决方案**：使用策略模式实现AI策略选择

```typescript
// AI策略接口
interface AIStrategy {
  execute(request: AIRequest): Promise<AIResponse>;
  getStrategyInfo(): StrategyInfo;
  isApplicable(context: StrategyContext): boolean;
}

// 策略上下文
interface StrategyContext {
  urgency: 'low' | 'medium' | 'high';
  qualityRequirement: 'basic' | 'standard' | 'premium';
  budgetConstraint: 'strict' | 'moderate' | 'flexible';
  responseTime: 'fast' | 'balanced' | 'thorough';
  userTier: 'free' | 'premium' | 'enterprise';
}

// 快速响应策略
class FastResponseStrategy implements AIStrategy {
  private aiService: UnifiedAIService;

  constructor() {
    this.aiService = AIServiceFactory.createService('openai', {
      model: 'gpt-3.5-turbo',
      temperature: 0.7,
      maxTokens: 500
    });
  }

  async execute(request: AIRequest): Promise<AIResponse> {
    const startTime = Date.now();

    // 使用简化的提示词以提高速度
    const simplifiedPrompt = this.simplifyPrompt(request.prompt);

    const response = await this.aiService.generateText({
      prompt: simplifiedPrompt,
      maxTokens: 500,
      temperature: 0.7
    });

    return {
      text: response.text,
      strategy: 'fast-response',
      processingTime: Date.now() - startTime,
      quality: 'basic',
      cost: 'low'
    };
  }

  getStrategyInfo(): StrategyInfo {
    return {
      name: 'Fast Response',
      description: '快速响应策略，优先速度',
      expectedResponseTime: '< 2秒',
      qualityLevel: 'basic',
      costLevel: 'low'
    };
  }

  isApplicable(context: StrategyContext): boolean {
    return context.urgency === 'high' ||
           context.responseTime === 'fast' ||
           context.budgetConstraint === 'strict';
  }

  private simplifyPrompt(prompt: string): string {
    // 简化提示词，移除不必要的细节
    return prompt.length > 200 ? prompt.substring(0, 200) + '...' : prompt;
  }
}

// 高质量策略
class HighQualityStrategy implements AIStrategy {
  private aiService: UnifiedAIService;

  constructor() {
    this.aiService = AIServiceFactory.createService('anthropic', {
      model: 'claude-3-opus',
      temperature: 0.3,
      maxTokens: 2000
    });
  }

  async execute(request: AIRequest): Promise<AIResponse> {
    const startTime = Date.now();

    // 构建详细的提示词
    const enhancedPrompt = this.enhancePrompt(request);

    const response = await this.aiService.generateText({
      prompt: enhancedPrompt,
      maxTokens: 2000,
      temperature: 0.3
    });

    // 质量验证
    const qualityScore = await this.validateQuality(response.text);

    return {
      text: response.text,
      strategy: 'high-quality',
      processingTime: Date.now() - startTime,
      quality: 'premium',
      cost: 'high',
      qualityScore
    };
  }

  getStrategyInfo(): StrategyInfo {
    return {
      name: 'High Quality',
      description: '高质量策略，优先质量',
      expectedResponseTime: '5-15秒',
      qualityLevel: 'premium',
      costLevel: 'high'
    };
  }

  isApplicable(context: StrategyContext): boolean {
    return context.qualityRequirement === 'premium' ||
           context.userTier === 'enterprise' ||
           context.budgetConstraint === 'flexible';
  }

  private enhancePrompt(request: AIRequest): string {
    return `
作为专业的AI助手，请仔细分析以下请求并提供高质量的回答：

用户请求：${request.prompt}

请确保回答：
1. 准确且详细
2. 逻辑清晰
3. 结构完整
4. 语言专业

回答：
`;
  }

  private async validateQuality(text: string): Promise<number> {
    // 简化的质量评估
    let score = 0.5;

    if (text.length > 100) score += 0.2;
    if (text.includes('因为') || text.includes('所以')) score += 0.1;
    if (text.split('。').length > 3) score += 0.1;
    if (!/重复/.test(text)) score += 0.1;

    return Math.min(score, 1.0);
  }
}

// 成本效益策略
class CostEffectiveStrategy implements AIStrategy {
  private primaryService: UnifiedAIService;
  private fallbackService: UnifiedAIService;

  constructor() {
    this.primaryService = AIServiceFactory.createService('openai', {
      model: 'gpt-3.5-turbo',
      temperature: 0.5,
      maxTokens: 1000
    });

    this.fallbackService = AIServiceFactory.createService('local', {
      model: 'llama-7b',
      temperature: 0.5,
      maxTokens: 1000
    });
  }

  async execute(request: AIRequest): Promise<AIResponse> {
    const startTime = Date.now();

    try {
      // 首先尝试主要服务
      const response = await this.primaryService.generateText({
        prompt: request.prompt,
        maxTokens: 1000,
        temperature: 0.5
      });

      return {
        text: response.text,
        strategy: 'cost-effective-primary',
        processingTime: Date.now() - startTime,
        quality: 'standard',
        cost: 'medium'
      };

    } catch (error) {
      // 降级到本地服务
      console.warn('Primary service failed, using fallback');

      const response = await this.fallbackService.generateText({
        prompt: request.prompt,
        maxTokens: 1000,
        temperature: 0.5
      });

      return {
        text: response.text,
        strategy: 'cost-effective-fallback',
        processingTime: Date.now() - startTime,
        quality: 'basic',
        cost: 'very-low'
      };
    }
  }

  getStrategyInfo(): StrategyInfo {
    return {
      name: 'Cost Effective',
      description: '成本效益策略，平衡质量和成本',
      expectedResponseTime: '3-8秒',
      qualityLevel: 'standard',
      costLevel: 'medium'
    };
  }

  isApplicable(context: StrategyContext): boolean {
    return context.budgetConstraint === 'moderate' ||
           context.qualityRequirement === 'standard' ||
           context.userTier === 'premium';
  }
}

// 策略管理器
class AIStrategyManager {
  private strategies: Map<string, AIStrategy> = new Map();
  private defaultStrategy: string = 'cost-effective';

  registerStrategy(name: string, strategy: AIStrategy): void {
    this.strategies.set(name, strategy);
  }

  setDefaultStrategy(name: string): void {
    if (!this.strategies.has(name)) {
      throw new Error(`Strategy '${name}' not found`);
    }
    this.defaultStrategy = name;
  }

  selectStrategy(context: StrategyContext): AIStrategy {
    // 根据上下文选择最适合的策略
    const applicableStrategies = Array.from(this.strategies.values())
      .filter(strategy => strategy.isApplicable(context));

    if (applicableStrategies.length === 0) {
      // 使用默认策略
      return this.strategies.get(this.defaultStrategy)!;
    }

    // 根据优先级选择策略
    if (context.urgency === 'high') {
      const fastStrategy = applicableStrategies.find(s =>
        s.getStrategyInfo().name === 'Fast Response'
      );
      if (fastStrategy) return fastStrategy;
    }

    if (context.qualityRequirement === 'premium') {
      const qualityStrategy = applicableStrategies.find(s =>
        s.getStrategyInfo().name === 'High Quality'
      );
      if (qualityStrategy) return qualityStrategy;
    }

    // 默认返回第一个适用的策略
    return applicableStrategies[0];
  }

  async executeWithBestStrategy(
    request: AIRequest,
    context: StrategyContext
  ): Promise<AIResponse> {
    const strategy = this.selectStrategy(context);

    console.log(`使用策略: ${strategy.getStrategyInfo().name}`);

    return await strategy.execute(request);
  }

  getAvailableStrategies(): StrategyInfo[] {
    return Array.from(this.strategies.values())
      .map(strategy => strategy.getStrategyInfo());
  }
}

// 使用示例
const strategyManager = new AIStrategyManager();

// 注册策略
strategyManager.registerStrategy('fast-response', new FastResponseStrategy());
strategyManager.registerStrategy('high-quality', new HighQualityStrategy());
strategyManager.registerStrategy('cost-effective', new CostEffectiveStrategy());

// 根据上下文执行
const context: StrategyContext = {
  urgency: 'medium',
  qualityRequirement: 'standard',
  budgetConstraint: 'moderate',
  responseTime: 'balanced',
  userTier: 'premium'
};

const request: AIRequest = {
  userId: 'user123',
  sessionId: 'session456',
  prompt: '请分析当前市场趋势并提供投资建议'
};

const response = await strategyManager.executeWithBestStrategy(request, context);
console.log('策略执行结果:', response);
```

---

## 🏛️ 架构型模式详解

### 1. AI Gateway Pattern

**问题**：需要统一管理多个AI服务的访问入口

**解决方案**：使用网关模式提供统一的AI服务入口

```typescript
// AI网关接口
interface AIGateway {
  route(request: GatewayRequest): Promise<GatewayResponse>;
  registerService(name: string, service: UnifiedAIService): void;
  getServiceHealth(): Promise<ServiceHealthStatus[]>;
}

// 网关请求
interface GatewayRequest {
  serviceType: 'text-generation' | 'image-generation' | 'text-analysis';
  provider?: string; // 可选的服务提供商
  payload: any;
  routing?: RoutingConfig;
  metadata?: RequestMetadata;
}

// 路由配置
interface RoutingConfig {
  strategy: 'round-robin' | 'weighted' | 'least-connections' | 'health-based';
  fallback: boolean;
  timeout: number;
  retries: number;
}

// AI网关实现
class EnterpriseAIGateway implements AIGateway {
  private services: Map<string, ServiceInfo> = new Map();
  private loadBalancer: LoadBalancer;
  private circuitBreaker: CircuitBreaker;
  private rateLimiter: GatewayRateLimiter;
  private monitor: GatewayMonitor;

  constructor(config: GatewayConfig) {
    this.loadBalancer = new LoadBalancer(config.loadBalancing);
    this.circuitBreaker = new CircuitBreaker(config.circuitBreaker);
    this.rateLimiter = new GatewayRateLimiter(config.rateLimiting);
    this.monitor = new GatewayMonitor();
  }

  registerService(name: string, service: UnifiedAIService): void {
    this.services.set(name, {
      name,
      service,
      health: 'healthy',
      lastHealthCheck: new Date(),
      requestCount: 0,
      errorCount: 0,
      averageLatency: 0
    });

    console.log(`Service '${name}' registered`);
  }

  async route(request: GatewayRequest): Promise<GatewayResponse> {
    const startTime = Date.now();

    try {
      // 1. 速率限制检查
      await this.rateLimiter.checkLimit(request);

      // 2. 选择服务
      const selectedService = await this.selectService(request);

      // 3. 熔断器检查
      if (!this.circuitBreaker.canExecute(selectedService.name)) {
        throw new Error(`Circuit breaker open for service: ${selectedService.name}`);
      }

      // 4. 执行请求
      const response = await this.executeRequest(selectedService, request);

      // 5. 更新指标
      this.updateServiceMetrics(selectedService.name, Date.now() - startTime, true);
      this.circuitBreaker.recordSuccess(selectedService.name);

      return {
        success: true,
        data: response,
        metadata: {
          service: selectedService.name,
          processingTime: Date.now() - startTime,
          requestId: this.generateRequestId()
        }
      };

    } catch (error) {
      // 记录错误
      this.monitor.recordError(request.serviceType, error);

      // 尝试降级处理
      if (request.routing?.fallback) {
        return await this.handleFallback(request, error);
      }

      return {
        success: false,
        error: error.message,
        metadata: {
          service: 'unknown',
          processingTime: Date.now() - startTime,
          requestId: this.generateRequestId()
        }
      };
    }
  }

  private async selectService(request: GatewayRequest): Promise<ServiceInfo> {
    // 如果指定了提供商，直接使用
    if (request.provider) {
      const service = this.services.get(request.provider);
      if (!service) {
        throw new Error(`Service '${request.provider}' not found`);
      }
      return service;
    }

    // 根据服务类型筛选可用服务
    const availableServices = Array.from(this.services.values())
      .filter(service => this.isServiceCompatible(service, request.serviceType))
      .filter(service => service.health === 'healthy');

    if (availableServices.length === 0) {
      throw new Error(`No healthy services available for ${request.serviceType}`);
    }

    // 使用负载均衡器选择服务
    return this.loadBalancer.selectService(
      availableServices,
      request.routing?.strategy || 'round-robin'
    );
  }

  private async executeRequest(
    serviceInfo: ServiceInfo,
    request: GatewayRequest
  ): Promise<any> {

    const timeout = request.routing?.timeout || 30000;
    const maxRetries = request.routing?.retries || 3;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        // 设置超时
        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => reject(new Error('Request timeout')), timeout);
        });

        // 执行请求
        const requestPromise = this.callService(serviceInfo.service, request);

        const result = await Promise.race([requestPromise, timeoutPromise]);
        return result;

      } catch (error) {
        console.warn(`Attempt ${attempt} failed for service ${serviceInfo.name}:`, error.message);

        if (attempt === maxRetries) {
          throw error;
        }

        // 指数退避
        await this.sleep(Math.pow(2, attempt) * 1000);
      }
    }
  }

  private async callService(service: UnifiedAIService, request: GatewayRequest): Promise<any> {
    switch (request.serviceType) {
      case 'text-generation':
        return await service.generateText(request.payload);

      case 'image-generation':
        return await service.generateImage(request.payload);

      case 'text-analysis':
        return await service.analyzeText(request.payload);

      default:
        throw new Error(`Unsupported service type: ${request.serviceType}`);
    }
  }

  private async handleFallback(
    request: GatewayRequest,
    originalError: Error
  ): Promise<GatewayResponse> {

    console.log('Executing fallback strategy...');

    // 尝试使用备用服务
    const fallbackServices = Array.from(this.services.values())
      .filter(service => service.health === 'degraded')
      .sort((a, b) => a.errorCount - b.errorCount);

    for (const fallbackService of fallbackServices) {
      try {
        const response = await this.callService(fallbackService.service, request);

        return {
          success: true,
          data: response,
          metadata: {
            service: fallbackService.name,
            fallback: true,
            originalError: originalError.message,
            requestId: this.generateRequestId()
          }
        };
      } catch (fallbackError) {
        console.warn(`Fallback service ${fallbackService.name} also failed`);
      }
    }

    // 所有服务都失败，返回错误
    throw new Error(`All services failed. Original error: ${originalError.message}`);
  }

  async getServiceHealth(): Promise<ServiceHealthStatus[]> {
    const healthStatuses: ServiceHealthStatus[] = [];

    for (const [name, serviceInfo] of this.services.entries()) {
      try {
        // 执行健康检查
        const healthCheckStart = Date.now();
        await this.performHealthCheck(serviceInfo.service);
        const healthCheckTime = Date.now() - healthCheckStart;

        serviceInfo.health = healthCheckTime < 5000 ? 'healthy' : 'degraded';
        serviceInfo.lastHealthCheck = new Date();

        healthStatuses.push({
          serviceName: name,
          status: serviceInfo.health,
          lastCheck: serviceInfo.lastHealthCheck,
          responseTime: healthCheckTime,
          errorRate: serviceInfo.errorCount / Math.max(serviceInfo.requestCount, 1)
        });

      } catch (error) {
        serviceInfo.health = 'unhealthy';
        serviceInfo.lastHealthCheck = new Date();

        healthStatuses.push({
          serviceName: name,
          status: 'unhealthy',
          lastCheck: serviceInfo.lastHealthCheck,
          error: error.message
        });
      }
    }

    return healthStatuses;
  }

  private async performHealthCheck(service: UnifiedAIService): Promise<void> {
    // 简单的健康检查
    await service.generateText({
      prompt: 'Health check',
      maxTokens: 10,
      temperature: 0
    });
  }

  private isServiceCompatible(service: ServiceInfo, serviceType: string): boolean {
    // 检查服务是否支持请求的类型
    // 这里简化处理，实际应该检查服务的能力
    return true;
  }

  private updateServiceMetrics(serviceName: string, latency: number, success: boolean): void {
    const service = this.services.get(serviceName);
    if (!service) return;

    service.requestCount++;
    service.averageLatency = (
      (service.averageLatency * (service.requestCount - 1) + latency) /
      service.requestCount
    );

    if (!success) {
      service.errorCount++;
    }
  }

  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// 使用示例
const gateway = new EnterpriseAIGateway({
  loadBalancing: {
    strategy: 'weighted',
    healthCheckInterval: 30000
  },
  circuitBreaker: {
    failureThreshold: 5,
    recoveryTimeout: 60000
  },
  rateLimiting: {
    requestsPerMinute: 1000,
    burstSize: 100
  }
});

// 注册服务
gateway.registerService('openai-gpt4', openaiService);
gateway.registerService('anthropic-claude', anthropicService);
gateway.registerService('google-gemini', googleService);

// 路由请求
const response = await gateway.route({
  serviceType: 'text-generation',
  payload: {
    prompt: '解释人工智能的发展历程',
    maxTokens: 1000,
    temperature: 0.7
  },
  routing: {
    strategy: 'health-based',
    fallback: true,
    timeout: 10000,
    retries: 2
  }
});

console.log('网关响应:', response);
```

---

## 💼 企业级AI助手平台案例

### 项目背景

构建一个企业级AI助手平台，集成多种AI能力，为企业用户提供智能化的业务支持。平台需要支持：

- **多种AI能力**：文档分析、内容生成、智能对话、工作流自动化
- **企业级特性**：安全控制、性能监控、可扩展架构
- **高级模式应用**：综合运用各种设计模式和架构模式

### 系统架构设计

```typescript
// 企业AI助手平台架构
class EnterpriseAIPlatform {
  private gateway: AIGateway;
  private assistant: EnterpriseAIAssistant;
  private modelFactory: AIModelFactory;
  private strategyManager: AIStrategyManager;
  private securityManager: SecurityManager;
  private monitoringService: MonitoringService;

  constructor(config: PlatformConfig) {
    this.initializeComponents(config);
    this.setupIntegrations();
  }

  private initializeComponents(config: PlatformConfig): void {
    // 初始化AI网关
    this.gateway = new EnterpriseAIGateway(config.gateway);

    // 初始化模型工厂
    this.modelFactory = new AIModelFactory();
    this.registerModels();

    // 初始化策略管理器
    this.strategyManager = new AIStrategyManager();
    this.registerStrategies();

    // 初始化核心助手
    this.assistant = new EnterpriseAIAssistant();

    // 初始化安全管理
    this.securityManager = new SecurityManager(config.security);

    // 初始化监控服务
    this.monitoringService = new MonitoringService(config.monitoring);
  }

  private registerModels(): void {
    this.modelFactory.registerConfigs({
      'fast-chat': {
        provider: 'openai',
        model: 'gpt-3.5-turbo',
        temperature: 0.7,
        maxTokens: 1000,
        rateLimiting: { requestsPerMinute: 200, tokensPerMinute: 20000 },
        caching: { enabled: true, ttl: 1800, maxSize: 500 }
      },
      'deep-analysis': {
        provider: 'anthropic',
        model: 'claude-3-opus',
        temperature: 0.3,
        maxTokens: 4000,
        rateLimiting: { requestsPerMinute: 50, tokensPerMinute: 10000 },
        caching: { enabled: true, ttl: 3600, maxSize: 200 }
      },
      'creative-writing': {
        provider: 'openai',
        model: 'gpt-4',
        temperature: 0.8,
        maxTokens: 2000,
        rateLimiting: { requestsPerMinute: 100, tokensPerMinute: 15000 },
        caching: { enabled: true, ttl: 2400, maxSize: 300 }
      }
    });
  }

  private registerStrategies(): void {
    this.strategyManager.registerStrategy('fast-response', new FastResponseStrategy());
    this.strategyManager.registerStrategy('high-quality', new HighQualityStrategy());
    this.strategyManager.registerStrategy('cost-effective', new CostEffectiveStrategy());
    this.strategyManager.registerStrategy('creative-enhanced', new CreativeEnhancedStrategy());
  }

  // 处理用户请求的主入口
  async processRequest(request: PlatformRequest): Promise<PlatformResponse> {
    const requestId = this.generateRequestId();
    const startTime = Date.now();

    try {
      // 1. 安全验证
      await this.securityManager.validateRequest(request);

      // 2. 用户认证和授权
      const userContext = await this.securityManager.authenticateUser(request.userId);
      await this.securityManager.authorizeRequest(userContext, request.capability);

      // 3. 策略选择
      const strategyContext = this.buildStrategyContext(request, userContext);
      const strategy = this.strategyManager.selectStrategy(strategyContext);

      // 4. 请求路由
      const gatewayRequest = this.buildGatewayRequest(request, strategy);
      const gatewayResponse = await this.gateway.route(gatewayRequest);

      // 5. 结果处理
      const processedResponse = await this.processResponse(
        gatewayResponse,
        request,
        userContext
      );

      // 6. 监控和日志
      this.monitoringService.recordRequest(requestId, request, processedResponse, true);

      return {
        success: true,
        data: processedResponse.data,
        metadata: {
          requestId,
          strategy: strategy.getStrategyInfo().name,
          processingTime: Date.now() - startTime,
          model: gatewayResponse.metadata?.service,
          tokenUsage: processedResponse.tokenUsage
        }
      };

    } catch (error) {
      // 错误处理和监控
      this.monitoringService.recordRequest(requestId, request, null, false, error);

      return {
        success: false,
        error: error.message,
        metadata: {
          requestId,
          processingTime: Date.now() - startTime
        }
      };
    }
  }

  private buildStrategyContext(
    request: PlatformRequest,
    userContext: UserContext
  ): StrategyContext {
    return {
      urgency: request.urgency || 'medium',
      qualityRequirement: request.qualityRequirement || 'standard',
      budgetConstraint: userContext.tier === 'enterprise' ? 'flexible' : 'moderate',
      responseTime: request.responseTime || 'balanced',
      userTier: userContext.tier
    };
  }

  private buildGatewayRequest(
    request: PlatformRequest,
    strategy: AIStrategy
  ): GatewayRequest {
    const strategyInfo = strategy.getStrategyInfo();

    return {
      serviceType: this.mapCapabilityToServiceType(request.capability),
      payload: request.input,
      routing: {
        strategy: 'health-based',
        fallback: true,
        timeout: strategyInfo.expectedResponseTime === '< 2秒' ? 5000 : 15000,
        retries: 2
      },
      metadata: {
        userId: request.userId,
        capability: request.capability,
        strategy: strategyInfo.name
      }
    };
  }

  private async processResponse(
    gatewayResponse: GatewayResponse,
    originalRequest: PlatformRequest,
    userContext: UserContext
  ): Promise<ProcessedResponse> {

    if (!gatewayResponse.success) {
      throw new Error(`Gateway error: ${gatewayResponse.error}`);
    }

    // 后处理响应
    const processedData = await this.postProcessResponse(
      gatewayResponse.data,
      originalRequest.capability,
      userContext
    );

    return {
      data: processedData,
      tokenUsage: this.estimateTokenUsage(gatewayResponse.data),
      quality: this.assessResponseQuality(processedData),
      compliance: await this.checkCompliance(processedData, userContext)
    };
  }

  private async postProcessResponse(
    data: any,
    capability: string,
    userContext: UserContext
  ): Promise<any> {

    // 根据能力类型进行特定的后处理
    switch (capability) {
      case 'document-analysis':
        return await this.postProcessAnalysis(data, userContext);

      case 'content-generation':
        return await this.postProcessContent(data, userContext);

      case 'intelligent-conversation':
        return await this.postProcessConversation(data, userContext);

      default:
        return data;
    }
  }

  // 获取平台统计信息
  async getPlatformStats(): Promise<PlatformStats> {
    const [
      gatewayHealth,
      modelStats,
      strategyStats,
      securityStats,
      monitoringStats
    ] = await Promise.all([
      this.gateway.getServiceHealth(),
      this.modelFactory.getModelStats(),
      this.strategyManager.getStrategyStats(),
      this.securityManager.getSecurityStats(),
      this.monitoringService.getStats()
    ]);

    return {
      gateway: {
        services: gatewayHealth,
        totalRequests: monitoringStats.totalRequests,
        averageLatency: monitoringStats.averageLatency
      },
      models: modelStats,
      strategies: strategyStats,
      security: securityStats,
      performance: {
        uptime: this.calculateUptime(),
        memoryUsage: process.memoryUsage(),
        cpuUsage: await this.getCPUUsage()
      }
    };
  }

  // 平台健康检查
  async healthCheck(): Promise<HealthStatus> {
    const checks = await Promise.allSettled([
      this.gateway.getServiceHealth(),
      this.checkDatabaseConnection(),
      this.checkCacheConnection(),
      this.checkExternalServices()
    ]);

    const healthyChecks = checks.filter(check => check.status === 'fulfilled').length;
    const totalChecks = checks.length;

    return {
      status: healthyChecks === totalChecks ? 'healthy' : 'degraded',
      checks: totalChecks,
      healthy: healthyChecks,
      timestamp: new Date(),
      details: checks.map((check, index) => ({
        name: ['gateway', 'database', 'cache', 'external'][index],
        status: check.status,
        error: check.status === 'rejected' ? check.reason.message : undefined
      }))
    };
  }

  // 辅助方法
  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private mapCapabilityToServiceType(capability: string): string {
    const mapping = {
      'document-analysis': 'text-analysis',
      'content-generation': 'text-generation',
      'intelligent-conversation': 'text-generation',
      'workflow-automation': 'text-generation'
    };

    return mapping[capability] || 'text-generation';
  }

  private estimateTokenUsage(data: any): number {
    const text = JSON.stringify(data);
    return Math.ceil(text.length / 4); // 简化的token估算
  }

  private assessResponseQuality(data: any): number {
    // 简化的质量评估
    const text = typeof data === 'string' ? data : JSON.stringify(data);

    let score = 0.5;
    if (text.length > 100) score += 0.2;
    if (text.includes('因为') || text.includes('所以')) score += 0.1;
    if (text.split('。').length > 3) score += 0.1;
    if (!/重复/.test(text)) score += 0.1;

    return Math.min(score, 1.0);
  }

  private async checkCompliance(data: any, userContext: UserContext): Promise<boolean> {
    // 合规性检查
    return await this.securityManager.checkDataCompliance(data, userContext);
  }

  private calculateUptime(): number {
    return process.uptime();
  }

  private async getCPUUsage(): Promise<number> {
    // 简化的CPU使用率获取
    return Math.random() * 100; // 实际应该使用系统API
  }

  private async checkDatabaseConnection(): Promise<void> {
    // 数据库连接检查
  }

  private async checkCacheConnection(): Promise<void> {
    // 缓存连接检查
  }

  private async checkExternalServices(): Promise<void> {
    // 外部服务检查
  }
}
```

---

## ❓ 常见问题解答

### Q1: 如何选择合适的AI编程模式？
**A**: 根据具体需求选择模式：
- **创建型模式**：当需要灵活创建和配置AI组件时
- **结构型模式**：当需要整合多个AI服务或添加横切关注点时
- **行为型模式**：当需要动态选择AI策略或构建处理流程时
- **架构型模式**：当构建大型AI系统时

### Q2: 如何平衡模式的复杂性和实用性？
**A**: 遵循以下原则：
- **渐进式应用**：从简单模式开始，逐步引入复杂模式
- **需求驱动**：只在确实需要时才引入模式
- **团队能力**：考虑团队的技术水平和维护能力
- **性能影响**：评估模式对系统性能的影响

### Q3: 如何确保AI系统的可扩展性？
**A**: 采用以下策略：
- **模块化设计**：清晰的模块边界和接口定义
- **插件架构**：支持动态加载和卸载组件
- **水平扩展**：支持分布式部署和负载均衡
- **配置驱动**：通过配置而非代码控制系统行为

### Q4: 如何监控和优化AI系统性能？
**A**: 建立全面的监控体系：
- **关键指标**：响应时间、吞吐量、错误率、资源使用
- **实时监控**：实时仪表板和告警机制
- **性能分析**：定期的性能分析和瓶颈识别
- **持续优化**：基于监控数据的持续优化

### Q5: 如何保证AI系统的安全性？
**A**: 多层次的安全保护：
- **身份认证**：强身份认证和会话管理
- **权限控制**：细粒度的权限管理
- **数据保护**：敏感数据加密和脱敏
- **审计跟踪**：完整的操作日志和审计

---

## 🚀 进阶练习

### 练习1：自定义AI模式
设计并实现一个新的AI编程模式：
- 识别特定的AI开发问题
- 设计模式解决方案
- 实现完整的代码示例
- 编写使用文档和最佳实践

### 练习2：性能优化挑战
优化现有的AI系统性能：
- 识别性能瓶颈
- 应用缓存和负载均衡
- 实现智能路由和降级
- 测试和验证优化效果

### 练习3：安全加固项目
为AI系统添加安全功能：
- 实现多因素认证
- 添加数据加密和脱敏
- 建立审计和监控机制
- 进行安全测试和评估

### 练习4：企业集成方案
设计企业级AI集成方案：
- 分析企业现有系统
- 设计集成架构
- 实现API网关和服务治理
- 制定部署和运维方案

---

## ✅ 模块完成检查清单

### 理论掌握
- [ ] 理解高级AI编程模式的分类和应用场景
- [ ] 掌握创建型、结构型、行为型、架构型模式
- [ ] 学会模式的组合使用和最佳实践
- [ ] 建立系统化的AI架构设计思维

### 技能实践
- [ ] 能够识别和应用合适的AI编程模式
- [ ] 掌握企业级AI系统的架构设计
- [ ] 具备AI系统性能优化的能力
- [ ] 建立了完整的监控和运维体系

### 项目成果
- [ ] 完成企业级AI助手平台的设计和实现
- [ ] 实现了多种高级AI编程模式
- [ ] 建立了可扩展、可维护的系统架构
- [ ] 系统具备企业级的安全和性能特性

### 工作流程
- [ ] 建立了模式驱动的开发流程
- [ ] 掌握了系统架构设计方法
- [ ] 具备了性能优化和问题诊断能力
- [ ] 能够进行企业级系统的部署和运维

### 自我评估问题
1. 您能根据需求选择和应用合适的AI编程模式吗？
2. 您的AI系统架构是否具备企业级的特性？
3. 您如何保证AI系统的性能、安全和可扩展性？
4. 您能设计和实现复杂的AI业务场景吗？
5. 这个AI助手平台对您的业务有什么价值？

---

## 📈 下一步学习建议

完成本模块后，建议您：

1. **深化实践**：在实际项目中应用高级AI编程模式
2. **架构演进**：持续优化和演进AI系统架构
3. **技术跟踪**：关注AI技术发展和新模式出现
4. **团队建设**：培养团队的AI架构设计能力

**恭喜您完成了AI编程课程的学习！**

---

## 🎓 课程总结

通过六个模块的学习，您已经掌握了：

1. **模块1**：AI编程基础 - BIG THREE核心概念
2. **模块2**：实战项目 - 智能客服系统
3. **模块3**：进阶技巧 - 高级提示词工程
4. **模块4**：错误处理 - 常见陷阱和解决方案
5. **模块5**：规格驱动 - 基于规格的AI编程
6. **模块6**：高级模式 - 企业级AI编程模式

您现在具备了构建企业级AI应用的完整能力，可以：
- 设计和实现复杂的AI系统
- 应用高级编程模式和架构模式
- 处理企业级的性能、安全和可扩展性需求
- 建立完整的开发、测试和运维流程

**继续您的AI编程之旅，创造更多价值！**

---

*💡 提示：高级AI编程模式是构建企业级AI系统的关键。通过合理应用这些模式，您可以构建出高质量、可维护、可扩展的AI应用。记住，模式是工具，关键是理解何时以及如何使用它们来解决实际问题。*
```
```
