{"name": "xia<PERSON>ongshu-cms-backend", "version": "1.0.0", "description": "小红书内容管理系统后端API", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "build": "tsc", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.js", "lint:fix": "eslint src/**/*.js --fix", "db:migrate": "sequelize-cli db:migrate", "db:seed": "sequelize-cli db:seed:all", "db:reset": "sequelize-cli db:drop && sequelize-cli db:create && npm run db:migrate && npm run db:seed"}, "keywords": ["cms", "content-management", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ai", "nodejs", "express"], "author": "Your Name", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "dotenv": "^16.3.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "multer": "^1.4.5-lts.1", "sequelize": "^6.35.2", "sqlite3": "^5.1.6", "mysql2": "^3.6.5", "pg": "^8.11.3", "redis": "^4.6.11", "socket.io": "^4.7.4", "node-cron": "^3.0.3", "nodemailer": "^6.9.7", "axios": "^1.6.2", "joi": "^17.11.0", "winston": "^3.11.0", "compression": "^1.7.4", "express-slow-down": "^2.0.1"}, "devDependencies": {"@types/node": "^20.10.4", "typescript": "^5.3.3", "nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3", "eslint": "^8.56.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-plugin-import": "^2.29.1", "sequelize-cli": "^6.6.2", "@babel/core": "^7.23.6", "@babel/preset-env": "^7.23.6", "babel-jest": "^29.7.0"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "jest": {"testEnvironment": "node", "collectCoverageFrom": ["src/**/*.js", "!src/config/**", "!src/migrations/**", "!src/seeders/**"], "coverageThreshold": {"global": {"branches": 80, "functions": 80, "lines": 80, "statements": 80}}}, "eslintConfig": {"extends": ["airbnb-base"], "env": {"node": true, "jest": true}, "rules": {"no-console": "warn", "no-unused-vars": "error", "prefer-const": "error"}}}