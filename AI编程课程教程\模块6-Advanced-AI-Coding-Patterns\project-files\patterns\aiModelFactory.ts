// AI模型工厂模式实现
// 提供统一的AI模型创建和管理接口

export interface AIModel {
  generate(prompt: string, options?: GenerationOptions): Promise<string>;
  generateStream(prompt: string, options?: GenerationOptions): AsyncIterable<string>;
  getCapabilities(): ModelCapabilities;
  getMetrics(): ModelMetrics;
  dispose(): Promise<void>;
}

export interface GenerationOptions {
  temperature?: number;
  maxTokens?: number;
  topP?: number;
  frequencyPenalty?: number;
  presencePenalty?: number;
  stop?: string[];
  timeout?: number;
}

export interface ModelCapabilities {
  maxContextLength: number;
  supportedLanguages: string[];
  supportedTasks: string[];
  multimodal: boolean;
  streaming: boolean;
  functionCalling: boolean;
}

export interface ModelMetrics {
  totalRequests: number;
  totalTokens: number;
  averageLatency: number;
  errorRate: number;
  lastUsed: Date;
}

export interface ModelConfig {
  provider: 'openai' | 'anthropic' | 'google' | 'local' | 'azure';
  model: string;
  apiKey?: string;
  endpoint?: string;
  temperature?: number;
  maxTokens?: number;
  timeout?: number;
  retryPolicy?: RetryPolicy;
  rateLimiting?: RateLimitConfig;
  caching?: CacheConfig;
}

export interface RetryPolicy {
  maxRetries: number;
  backoffMultiplier: number;
  maxBackoffTime: number;
  retryableErrors: string[];
}

export interface RateLimitConfig {
  requestsPerMinute: number;
  tokensPerMinute: number;
  burstSize?: number;
}

export interface CacheConfig {
  enabled: boolean;
  ttl: number; // seconds
  maxSize: number; // number of entries
  strategy: 'lru' | 'lfu' | 'ttl';
}

// 抽象AI模型基类
abstract class BaseAIModel implements AIModel {
  protected config: ModelConfig;
  protected metrics: ModelMetrics;
  protected rateLimiter?: RateLimiter;
  protected cache?: ModelCache;

  constructor(config: ModelConfig) {
    this.config = config;
    this.metrics = {
      totalRequests: 0,
      totalTokens: 0,
      averageLatency: 0,
      errorRate: 0,
      lastUsed: new Date()
    };

    if (config.rateLimiting) {
      this.rateLimiter = new RateLimiter(config.rateLimiting);
    }

    if (config.caching?.enabled) {
      this.cache = new ModelCache(config.caching);
    }
  }

  async generate(prompt: string, options?: GenerationOptions): Promise<string> {
    const startTime = Date.now();
    
    try {
      // 检查缓存
      if (this.cache) {
        const cacheKey = this.generateCacheKey(prompt, options);
        const cached = await this.cache.get(cacheKey);
        if (cached) {
          return cached;
        }
      }

      // 速率限制
      if (this.rateLimiter) {
        await this.rateLimiter.acquire();
      }

      // 执行生成
      const result = await this.doGenerate(prompt, options);

      // 更新指标
      this.updateMetrics(startTime, result.length, false);

      // 缓存结果
      if (this.cache) {
        const cacheKey = this.generateCacheKey(prompt, options);
        await this.cache.set(cacheKey, result);
      }

      return result;

    } catch (error) {
      this.updateMetrics(startTime, 0, true);
      throw error;
    }
  }

  async *generateStream(prompt: string, options?: GenerationOptions): AsyncIterable<string> {
    const startTime = Date.now();
    let totalLength = 0;
    let hasError = false;

    try {
      // 速率限制
      if (this.rateLimiter) {
        await this.rateLimiter.acquire();
      }

      // 执行流式生成
      for await (const chunk of this.doGenerateStream(prompt, options)) {
        totalLength += chunk.length;
        yield chunk;
      }

    } catch (error) {
      hasError = true;
      throw error;
    } finally {
      this.updateMetrics(startTime, totalLength, hasError);
    }
  }

  // 抽象方法，由具体实现类重写
  protected abstract doGenerate(prompt: string, options?: GenerationOptions): Promise<string>;
  protected abstract doGenerateStream(prompt: string, options?: GenerationOptions): AsyncIterable<string>;
  
  abstract getCapabilities(): ModelCapabilities;

  getMetrics(): ModelMetrics {
    return { ...this.metrics };
  }

  async dispose(): Promise<void> {
    if (this.cache) {
      await this.cache.clear();
    }
  }

  private generateCacheKey(prompt: string, options?: GenerationOptions): string {
    const optionsStr = options ? JSON.stringify(options) : '';
    return `${this.config.model}:${prompt}:${optionsStr}`;
  }

  private updateMetrics(startTime: number, tokenCount: number, hasError: boolean): void {
    const latency = Date.now() - startTime;
    
    this.metrics.totalRequests++;
    this.metrics.totalTokens += tokenCount;
    this.metrics.averageLatency = (
      (this.metrics.averageLatency * (this.metrics.totalRequests - 1) + latency) / 
      this.metrics.totalRequests
    );
    
    if (hasError) {
      this.metrics.errorRate = (
        (this.metrics.errorRate * (this.metrics.totalRequests - 1) + 1) / 
        this.metrics.totalRequests
      );
    } else {
      this.metrics.errorRate = (
        this.metrics.errorRate * (this.metrics.totalRequests - 1) / 
        this.metrics.totalRequests
      );
    }
    
    this.metrics.lastUsed = new Date();
  }
}

// OpenAI模型实现
class OpenAIModel extends BaseAIModel {
  private client: any; // OpenAI client

  constructor(config: ModelConfig) {
    super(config);
    this.client = new OpenAI({
      apiKey: config.apiKey,
      baseURL: config.endpoint
    });
  }

  protected async doGenerate(prompt: string, options?: GenerationOptions): Promise<string> {
    const response = await this.client.chat.completions.create({
      model: this.config.model,
      messages: [{ role: 'user', content: prompt }],
      temperature: options?.temperature ?? this.config.temperature,
      max_tokens: options?.maxTokens ?? this.config.maxTokens,
      top_p: options?.topP,
      frequency_penalty: options?.frequencyPenalty,
      presence_penalty: options?.presencePenalty,
      stop: options?.stop
    });

    return response.choices[0]?.message?.content || '';
  }

  protected async *doGenerateStream(prompt: string, options?: GenerationOptions): AsyncIterable<string> {
    const stream = await this.client.chat.completions.create({
      model: this.config.model,
      messages: [{ role: 'user', content: prompt }],
      temperature: options?.temperature ?? this.config.temperature,
      max_tokens: options?.maxTokens ?? this.config.maxTokens,
      stream: true
    });

    for await (const chunk of stream) {
      const content = chunk.choices[0]?.delta?.content;
      if (content) {
        yield content;
      }
    }
  }

  getCapabilities(): ModelCapabilities {
    return {
      maxContextLength: this.getMaxContextLength(),
      supportedLanguages: ['en', 'zh', 'es', 'fr', 'de', 'ja', 'ko'],
      supportedTasks: ['text-generation', 'conversation', 'analysis', 'translation'],
      multimodal: this.config.model.includes('vision'),
      streaming: true,
      functionCalling: this.config.model.includes('gpt-4') || this.config.model.includes('gpt-3.5')
    };
  }

  private getMaxContextLength(): number {
    if (this.config.model.includes('gpt-4-turbo')) return 128000;
    if (this.config.model.includes('gpt-4')) return 8192;
    if (this.config.model.includes('gpt-3.5-turbo')) return 4096;
    return 4096;
  }
}

// AI模型工厂
export class AIModelFactory {
  private static modelCache = new Map<string, AIModel>();
  private static configRegistry = new Map<string, ModelConfig>();

  // 注册模型配置
  static registerConfig(name: string, config: ModelConfig): void {
    this.configRegistry.set(name, config);
  }

  // 批量注册配置
  static registerConfigs(configs: Record<string, ModelConfig>): void {
    for (const [name, config] of Object.entries(configs)) {
      this.registerConfig(name, config);
    }
  }

  // 创建模型实例
  static async createModel(
    configName: string,
    overrides?: Partial<ModelConfig>
  ): Promise<AIModel> {
    
    const cacheKey = `${configName}_${JSON.stringify(overrides)}`;
    
    // 检查缓存
    if (this.modelCache.has(cacheKey)) {
      return this.modelCache.get(cacheKey)!;
    }

    // 获取配置
    const baseConfig = this.configRegistry.get(configName);
    if (!baseConfig) {
      throw new Error(`Model config '${configName}' not found`);
    }

    const finalConfig = { ...baseConfig, ...overrides };
    
    // 根据提供商创建模型
    let model: AIModel;
    switch (finalConfig.provider) {
      case 'openai':
        model = new OpenAIModel(finalConfig);
        break;
      case 'anthropic':
        model = new AnthropicModel(finalConfig);
        break;
      case 'google':
        model = new GoogleModel(finalConfig);
        break;
      case 'local':
        model = new LocalModel(finalConfig);
        break;
      case 'azure':
        model = new AzureOpenAIModel(finalConfig);
        break;
      default:
        throw new Error(`Unsupported provider: ${finalConfig.provider}`);
    }

    // 缓存模型实例
    this.modelCache.set(cacheKey, model);
    
    return model;
  }

  // 创建模型集合
  static async createModelEnsemble(
    configs: Array<{ name: string; weight: number }>
  ): Promise<ModelEnsemble> {
    
    const models = await Promise.all(
      configs.map(async config => ({
        model: await this.createModel(config.name),
        weight: config.weight
      }))
    );

    return new ModelEnsemble(models);
  }

  // 获取已注册的配置
  static getRegisteredConfigs(): string[] {
    return Array.from(this.configRegistry.keys());
  }

  // 清理缓存
  static async clearCache(): Promise<void> {
    for (const model of this.modelCache.values()) {
      await model.dispose();
    }
    this.modelCache.clear();
  }

  // 获取模型统计信息
  static getModelStats(): Record<string, ModelMetrics> {
    const stats: Record<string, ModelMetrics> = {};
    for (const [key, model] of this.modelCache.entries()) {
      stats[key] = model.getMetrics();
    }
    return stats;
  }
}

// 模型集合类
export class ModelEnsemble implements AIModel {
  private models: Array<{ model: AIModel; weight: number }>;
  private totalWeight: number;

  constructor(models: Array<{ model: AIModel; weight: number }>) {
    this.models = models;
    this.totalWeight = models.reduce((sum, m) => sum + m.weight, 0);
  }

  async generate(prompt: string, options?: GenerationOptions): Promise<string> {
    // 根据权重选择模型
    const selectedModel = this.selectModel();
    return await selectedModel.generate(prompt, options);
  }

  async *generateStream(prompt: string, options?: GenerationOptions): AsyncIterable<string> {
    const selectedModel = this.selectModel();
    yield* selectedModel.generateStream(prompt, options);
  }

  getCapabilities(): ModelCapabilities {
    // 返回所有模型能力的并集
    const allCapabilities = this.models.map(m => m.model.getCapabilities());
    
    return {
      maxContextLength: Math.max(...allCapabilities.map(c => c.maxContextLength)),
      supportedLanguages: [...new Set(allCapabilities.flatMap(c => c.supportedLanguages))],
      supportedTasks: [...new Set(allCapabilities.flatMap(c => c.supportedTasks))],
      multimodal: allCapabilities.some(c => c.multimodal),
      streaming: allCapabilities.some(c => c.streaming),
      functionCalling: allCapabilities.some(c => c.functionCalling)
    };
  }

  getMetrics(): ModelMetrics {
    // 聚合所有模型的指标
    const allMetrics = this.models.map(m => m.model.getMetrics());
    
    return {
      totalRequests: allMetrics.reduce((sum, m) => sum + m.totalRequests, 0),
      totalTokens: allMetrics.reduce((sum, m) => sum + m.totalTokens, 0),
      averageLatency: allMetrics.reduce((sum, m) => sum + m.averageLatency, 0) / allMetrics.length,
      errorRate: allMetrics.reduce((sum, m) => sum + m.errorRate, 0) / allMetrics.length,
      lastUsed: new Date(Math.max(...allMetrics.map(m => m.lastUsed.getTime())))
    };
  }

  async dispose(): Promise<void> {
    await Promise.all(this.models.map(m => m.model.dispose()));
  }

  private selectModel(): AIModel {
    const random = Math.random() * this.totalWeight;
    let currentWeight = 0;
    
    for (const { model, weight } of this.models) {
      currentWeight += weight;
      if (random <= currentWeight) {
        return model;
      }
    }
    
    // 默认返回第一个模型
    return this.models[0].model;
  }
}

// 辅助类
class RateLimiter {
  private config: RateLimitConfig;
  private requestQueue: Array<() => void> = [];
  private requestCount = 0;
  private tokenCount = 0;
  private lastReset = Date.now();

  constructor(config: RateLimitConfig) {
    this.config = config;
    this.startResetTimer();
  }

  async acquire(): Promise<void> {
    return new Promise((resolve) => {
      if (this.canProceed()) {
        this.requestCount++;
        resolve();
      } else {
        this.requestQueue.push(resolve);
      }
    });
  }

  private canProceed(): boolean {
    this.resetIfNeeded();
    return this.requestCount < this.config.requestsPerMinute;
  }

  private resetIfNeeded(): void {
    const now = Date.now();
    if (now - this.lastReset >= 60000) { // 1 minute
      this.requestCount = 0;
      this.tokenCount = 0;
      this.lastReset = now;
      this.processQueue();
    }
  }

  private processQueue(): void {
    while (this.requestQueue.length > 0 && this.canProceed()) {
      const resolve = this.requestQueue.shift()!;
      this.requestCount++;
      resolve();
    }
  }

  private startResetTimer(): void {
    setInterval(() => {
      this.resetIfNeeded();
    }, 1000);
  }
}

class ModelCache {
  private cache = new Map<string, { value: string; timestamp: number }>();
  private config: CacheConfig;

  constructor(config: CacheConfig) {
    this.config = config;
    this.startCleanupTimer();
  }

  async get(key: string): Promise<string | null> {
    const entry = this.cache.get(key);
    if (!entry) return null;

    // 检查TTL
    if (Date.now() - entry.timestamp > this.config.ttl * 1000) {
      this.cache.delete(key);
      return null;
    }

    return entry.value;
  }

  async set(key: string, value: string): Promise<void> {
    // 检查缓存大小
    if (this.cache.size >= this.config.maxSize) {
      this.evict();
    }

    this.cache.set(key, {
      value,
      timestamp: Date.now()
    });
  }

  async clear(): Promise<void> {
    this.cache.clear();
  }

  private evict(): void {
    // 简单的LRU实现
    const oldestKey = this.cache.keys().next().value;
    if (oldestKey) {
      this.cache.delete(oldestKey);
    }
  }

  private startCleanupTimer(): void {
    setInterval(() => {
      const now = Date.now();
      for (const [key, entry] of this.cache.entries()) {
        if (now - entry.timestamp > this.config.ttl * 1000) {
          this.cache.delete(key);
        }
      }
    }, 60000); // 每分钟清理一次
  }
}

// 占位符类（需要具体实现）
class AnthropicModel extends BaseAIModel {
  protected async doGenerate(prompt: string, options?: GenerationOptions): Promise<string> {
    throw new Error('AnthropicModel implementation needed');
  }
  
  protected async *doGenerateStream(prompt: string, options?: GenerationOptions): AsyncIterable<string> {
    throw new Error('AnthropicModel implementation needed');
  }
  
  getCapabilities(): ModelCapabilities {
    throw new Error('AnthropicModel implementation needed');
  }
}

class GoogleModel extends BaseAIModel {
  protected async doGenerate(prompt: string, options?: GenerationOptions): Promise<string> {
    throw new Error('GoogleModel implementation needed');
  }
  
  protected async *doGenerateStream(prompt: string, options?: GenerationOptions): AsyncIterable<string> {
    throw new Error('GoogleModel implementation needed');
  }
  
  getCapabilities(): ModelCapabilities {
    throw new Error('GoogleModel implementation needed');
  }
}

class LocalModel extends BaseAIModel {
  protected async doGenerate(prompt: string, options?: GenerationOptions): Promise<string> {
    throw new Error('LocalModel implementation needed');
  }
  
  protected async *doGenerateStream(prompt: string, options?: GenerationOptions): AsyncIterable<string> {
    throw new Error('LocalModel implementation needed');
  }
  
  getCapabilities(): ModelCapabilities {
    throw new Error('LocalModel implementation needed');
  }
}

class AzureOpenAIModel extends BaseAIModel {
  protected async doGenerate(prompt: string, options?: GenerationOptions): Promise<string> {
    throw new Error('AzureOpenAIModel implementation needed');
  }
  
  protected async *doGenerateStream(prompt: string, options?: GenerationOptions): AsyncIterable<string> {
    throw new Error('AzureOpenAIModel implementation needed');
  }
  
  getCapabilities(): ModelCapabilities {
    throw new Error('AzureOpenAIModel implementation needed');
  }
}
