# AI任务澄清助手 - 模块2：Multi-File Editing

## 📋 文档目标

本文档旨在帮助学习者使用AI工具来澄清和优化多文件项目开发任务，特别是围绕前后端分离架构、组件化设计和AI协作开发的复杂任务。

## 🎯 任务澄清提示词模板

### 多文件项目分析模板

```
你是一位全栈开发和AI协作专家，请帮我分析以下多文件项目任务：

**项目任务描述**：
[在此处粘贴你的项目任务描述]

**项目复杂度分析**：
1. 项目规模和范围是否合理？
2. 技术栈选择是否明确？
3. 文件结构和组织是否清晰？
4. 前后端分离的边界是否明确？
5. 数据流和接口设计是否完整？

**AI协作要求检查**：
- 每个文件的AI生成要求是否明确？
- 文件间的依赖关系是否清楚？
- 集成测试的策略是否考虑？
- 版本控制和协作流程是否定义？

**请提供**：
1. 项目可行性评估（1-10分）
2. 技术架构建议
3. 文件结构优化方案
4. AI协作策略建议
5. 潜在风险和解决方案
```

### 架构设计澄清模板

```
作为软件架构师，请帮我澄清以下项目的架构设计：

**项目背景**：
[描述项目的业务需求和技术要求]

**架构澄清要求**：

**前端架构**：
- 选择什么前端框架？为什么？
- 组件化策略是什么？
- 状态管理如何设计？
- 路由和导航如何组织？

**后端架构**：
- API设计风格（RESTful/GraphQL）？
- 数据库设计策略？
- 业务逻辑分层方案？
- 错误处理和日志策略？

**集成架构**：
- 前后端通信协议？
- 数据格式和验证规则？
- 认证和授权机制？
- 部署和运维策略？

**输出要求**：
请提供详细的架构设计文档，包括技术选型理由、架构图、接口规范和实施计划。
```

## ✅ 任务检查清单

### 项目规划检查

- [ ] **需求分析完整性**
  - 功能需求是否明确？
  - 非功能需求是否考虑？
  - 用户角色和使用场景清楚吗？
  - 业务流程是否梳理清晰？

- [ ] **技术选型合理性**
  - 前端技术栈选择合适吗？
  - 后端技术栈选择合适吗？
  - 数据库选择符合需求吗？
  - 第三方库和工具选择合理吗？

- [ ] **项目范围控制**
  - 项目规模是否适中？
  - 开发周期是否合理？
  - 资源需求是否明确？
  - 风险评估是否充分？

### 架构设计检查

- [ ] **前端架构**
  - 组件层次结构清晰吗？
  - 状态管理策略明确吗？
  - 路由设计合理吗？
  - UI/UX设计考虑了吗？

- [ ] **后端架构**
  - API设计规范吗？
  - 数据模型设计合理吗？
  - 业务逻辑分层清晰吗？
  - 安全性考虑充分吗？

- [ ] **数据架构**
  - 数据库设计规范吗？
  - 数据流设计合理吗？
  - 数据验证策略明确吗？
  - 数据备份和恢复考虑了吗？

### AI协作检查

- [ ] **任务分解**
  - 每个文件的生成任务明确吗？
  - 任务间的依赖关系清楚吗？
  - 任务优先级排序合理吗？
  - 任务完成标准明确吗？

- [ ] **协作流程**
  - AI工具使用策略明确吗？
  - 代码审查流程定义了吗？
  - 集成测试计划制定了吗？
  - 版本控制策略明确吗？

## 🤝 AI协作指南

### 多文件项目协作策略

1. **分层协作方法**
   - **架构层**：使用AI设计整体架构和技术选型
   - **模块层**：使用AI生成独立的功能模块
   - **文件层**：使用AI生成具体的代码文件
   - **集成层**：使用AI协助集成测试和优化

2. **迭代开发流程**
   - **需求澄清**：使用AI分析和完善需求
   - **架构设计**：使用AI生成架构方案
   - **代码生成**：使用AI生成核心代码
   - **测试验证**：使用AI生成测试用例
   - **优化改进**：使用AI分析和优化代码

### 协作最佳实践

- **保持一致性**：确保所有AI生成的代码风格一致
- **模块化思维**：将复杂任务分解为独立的模块
- **接口优先**：先定义接口，再实现具体功能
- **测试驱动**：为每个模块生成相应的测试

## ❓ 常见问题模板

### 项目规划类问题

```
关于项目规划，请帮我澄清：
1. 这个项目的核心价值是什么？
2. 最小可行产品（MVP）应该包含哪些功能？
3. 项目的技术难点和风险点在哪里？
4. 如何制定合理的开发计划和里程碑？
5. 需要哪些外部资源和依赖？
```

### 架构设计类问题

```
在架构设计方面，我需要确认：
1. 前后端分离的边界应该如何划分？
2. 数据库设计应该考虑哪些因素？
3. API设计应该遵循什么原则？
4. 如何处理跨域、认证、权限等问题？
5. 如何设计可扩展和可维护的架构？
```

### AI协作类问题

```
关于AI协作开发，请指导：
1. 如何有效地将项目任务分解给AI？
2. 如何确保AI生成的代码质量？
3. 如何处理AI生成代码的集成问题？
4. 如何建立有效的代码审查流程？
5. 如何利用AI进行项目管理和进度跟踪？
```

### 技术实现类问题

```
在技术实现细节上，请帮助：
1. 前端状态管理应该如何设计？
2. 后端API的错误处理策略是什么？
3. 数据库查询优化应该考虑什么？
4. 如何实现高效的前后端通信？
5. 部署和运维应该注意哪些问题？
```

## 🚀 任务优化建议

### 基于模块2特点的优化方向

1. **强化架构思维**
   - 从整体到局部的设计思路
   - 模块化和组件化的设计原则
   - 可扩展和可维护的架构模式

2. **提升协作效率**
   - 标准化的开发流程
   - 自动化的构建和测试
   - 有效的沟通和协调机制

3. **注重代码质量**
   - 统一的编码规范
   - 完善的测试覆盖
   - 持续的代码审查

### 项目管理优化策略

1. **敏捷开发方法**
   - 短周期迭代开发
   - 持续集成和部署
   - 快速反馈和调整

2. **风险管理**
   - 技术风险识别和应对
   - 进度风险监控和控制
   - 质量风险预防和处理

3. **团队协作**
   - 明确的角色分工
   - 有效的沟通机制
   - 共享的知识库

## 📝 使用示例

### 示例1：博客系统项目澄清

**原始任务**：
"创建一个博客网站"

**澄清后的任务**：
"开发一个现代化的个人博客系统，包含以下功能：
- **前端**：React + TypeScript，支持文章浏览、搜索、分类、评论
- **后端**：Node.js + Express，提供RESTful API，用户认证，内容管理
- **数据库**：MongoDB，存储文章、用户、评论数据
- **部署**：Docker容器化，支持CI/CD自动部署
- **AI协作**：使用AI生成组件代码、API接口、测试用例
- **交付标准**：完整的源码、部署文档、用户手册"

### 示例2：电商系统架构澄清

**需求**：开发一个简单的电商平台

**澄清结果**：
- **前端架构**：Vue.js + Vuex + Vue Router，组件化UI设计
- **后端架构**：Spring Boot + MyBatis，分层架构设计
- **数据库设计**：MySQL，商品、订单、用户表设计
- **接口设计**：RESTful API，统一的响应格式
- **安全设计**：JWT认证，HTTPS传输，SQL注入防护

## 🎯 预期效果

通过使用本澄清助手，学习者应该能够：

1. **明确项目范围**：清楚项目的边界和核心功能
2. **优化技术选型**：选择合适的技术栈和工具
3. **设计合理架构**：创建可扩展和可维护的系统架构
4. **制定开发计划**：分解任务，制定合理的开发时间线
5. **建立协作流程**：有效利用AI工具提高开发效率

---

*💡 提示：多文件项目的成功关键在于良好的规划和架构设计。充分利用AI的分析和设计能力，可以帮助你构建更优秀的软件系统。*
