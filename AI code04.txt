我觉得我自己和大家都会犯的最大错误就是，试图匆忙地处理上下文，没有耐心告诉 AI 它解决问题实际需要知道的信息。我认为如果我们都能稍微慢一点，完成这两个步骤，反而会加快所有事情的进度。
没有人真正知道该怎么做这些事。唯一能搞明白的方法就是亲自上手，看看什么方法有效。很多工程师和产品经理就是在这里陷入了困境，比如谁来接手这个PR（产品需求文档），并将其分解成正确的步骤。
所以，即便是这样，对于构建产品、创建这家新初创公司的人来说，也极大地节省了时间，我真的觉得自己能够完成所有这些工作。我能像一个专门的产品经理那样做得好吗？不能。我能像首席技术官那样深入思考吗？不能。但我确信能够建立这家公司。朋友们，我告诉你们，这就是方法。注意听。欢迎来到《我如何使用 AI》。
我是克莱尔，产品负责人和 AI 爱好者，我的使命是帮助你用这些新工具更好地进行构建。2025年绝对是“随心所欲编码者”的一年，但你不能总是凭感觉就能制定出可扩展的执行策略。在这一集中，拥有20年经验、五次创业的瑞安·卡森将向我们展示他如何将 PRD（产品需求文档）、任务列表和一些高级提示技巧引入 Cursor，以确保他不仅仅是凭感觉，而是在构建正确的东西。
我们开始吧。今天的节目由 ChatPD 为您带来。我知道你们很多人收听《我如何使用 AI》是为了学习应用 AI 并使构建更容易的实用方法。这正是我构建 ChatPD 的原因。Chat PRD 是一款 AI 协opilot，可以帮助你编写出色的产品文档，自动化繁琐的协调工作，并从专家级 AI CPO（首席产品官）那里获得战略指导。
它深受从发展最快的 AI 初创公司到拥有数百名产品经理的大型企业的喜爱。无论你是想凭感觉编写原型代码，教导初任产品经理，还是在大型组织中高效扩展，Chat PRD 都能帮助你更快地完成更好的工作。而且我们与你喜爱的工具集成：Vercel、
Dev、Google Drive、Slack、Linear、Confluence 等等。所以你无需改变工作流程就能通过 AI 加速。在 chaturd.ai/howiai 免费试用 ChatPRD。让我们一起让产品再次变得有趣。嘿，瑞安，很高兴你能来。谢谢。能来到这里我感到很兴奋。到目前为止，我听了每一集，能亲自参与我感到很荣幸。
我等不及了。那么，我从一个简单的问题开始，你最近用 AI 构建的三样东西是什么？我不知道你是否把经常和孩子们一起使用 ChatGPT 称作在 AI 中构建东西，但我感觉自己就像家里的常驻 AI 教练，而且我总是对孩子们正在做的事情感到非常高兴。
正因为如此，嗯，我那个超棒的 14 岁孩子 Devon 对我说：“爸爸，你知道吗，我一直在构思一个游戏。”我说：“那好吧，我们来做吧。”于是我们正在开发一个简单的横版过关游戏，他就像是创意总监。哦，这太有趣了。
所以这算是第一件和第二件事。我想说的第三件事就是我正在创建的初创公司，这涉及到巨大的编码工作量。但除此之外，我随时都在凭感觉快速编码处理各种小事情。所以，每天，无时无刻，聊天都应该是关于凭感觉编码的。我要给我们都做件 T 恤，上面印着“讨厌”。
好的。那么，我认为你比其他凭感觉编码的人做得更好的一点是，你为这个过程带来了一些结构。你使用 Cursor 的方式非常明智，所以我希望你能分享你的屏幕，向我们展示你是如何让 Cursor 遵循计划的。我们开始吧。
之所以我这样做，是因为我一直在用 AI 编码、编码再编码，你边做边学就会发现，你必须非常擅长处理上下文，也就是你向 AI 展示什么，你要求 AI 做什么。最终，你会把所有正在做的事情削减到 AI 实际能够处理的可管理范围内。
所以，我现在要向你们展示的流程，我正在使用 Cursor。如果你正在收听这个节目，你可以在 cursor.com 免费下载它。它基本上是 VS Code 的一个分支，对吧？所以，如果你用过 VS Code，它是个很棒的工具。那么，我在这里做的是打开一个我昨天凭感觉编码的基础项目，只是为了向你们展示一下情况。
这是一个为一个游艇俱乐部制作的傻瓜式小型 CRM 工具，因为我觉得那会有点好笑。所以，就是这样。那么，好吧。假设你想在这里做一个改动，而且这个改动比一个小小的，你知道，一个快速的“嘿，你能不能改一下这个东西”要大。假设它更大，好吧，那么你可能想创建一个产品需求文档，对吧？比如 PRD。如果你正在观看节目并且不知道克莱尔的 Chat PRD，一定要去看看，因为它太棒了。但如果你需要一个更轻量级的方案，那么这就是我的做法。
我在我的 Cursor 规则文件夹里创建了三个文件，我会带你们了解它们的功能和工作方式。我还把这些文件开源了，所以我们可以在节目笔记中提供一个链接供大家获取。第一个是一个简单的指令，让 AI 创建产品需求文档。
再次强调，克莱尔对此了如指掌，并以此为生，但对许多不了解 PRD 的人来说，对吧，它就是你描述你想要构建的功能的方式。所以这个规则向 AI 解释了如何为用户编写 PRD。使用这个的方法非常简单，对吧？所以我要过去，我想快速暂停一下，因为我喜欢你最初的提示，它说这是一个适合初级开发人员理解和实现此功能的 PRD。
这个小提示很有意思。是的，你观察得很仔细。因为当你和 AI 一起不断编码时，你会开始意识到它们就像一个天才博士生，对吧？但它们似乎无法将你我都知道的那些非常简单明显的事情联系起来。
所以说“初级开发人员”有点像是在指示 AI，让我们把事情保持在一定水平上。是的。你知道，我们有一些流程，有一些澄清问题。你会看到它实际运作的样子。我会实际运行它。太好了。那么，我们开始吧。太好了。所以，我会引用这个文件，把它放到上下文窗口中，然后我会给它一个我预先写好的简单指令，那就是：我想要添加一个报告，显示所有会员的船名以及他们收到了多少封邮件。
如果你在游艇俱乐部，这可是件大事。你有更多有趣的用例来提供编码。我尽力而为，你知道的。那么，我们来看看会发生什么。所以我将点击“开始”。我们现在处于代理模式。对于那些敏锐的人，你会注意到我正在使用 Claude 3.7 Sonnet 的 Max 模式。
我实际上倾向于使用 Gemini 2.5 Pro。有趣的是，我没注意到它选择了那个，但我现在是默认的 03（Claude 3 Opus）女孩。哦哦。哦哦。我们得谈谈。是的。然后当 03 卡住的时候，我就会换到 3.7（Claude 3.Sonnet）。对。对。真有趣。我基本上默认使用 Gemini 2.5 Pro。我喜欢它的 Max 模式。它确实有点贵。
我可能每个月要花三四百美元。嗯，但是，值得。我觉得还好。好的。那么，我们现在得到的是 AI 回复了我们一些关于 PRD 的澄清问题。我会回答其中几个，向你们展示这是如何运作的。所以，如果你在听，AI 说了：“好的，太好了。
我会帮你创建一份 PRD。” 嗯，有几个问题想问你。第一个是，“这个报告要解决什么问题？”嗯，我们是……这是什么问题？我们试图让人们了解他们收到了多少邮件，对吧？好的。这是第一件事。我们继续。我们来回答第二个问题。
具体谁会使用这份报告？好的。我们说是管理员。这份报告应该在哪里可以访问？你来定。这里面可能有太多问题了，但我想让所有观看的人习惯的一件事是，你会注意到在这个 PRD 规则文件中，我做了一些特定的事情，我说我希望这些问题是点符号表示的，比如 2.
1 和 2.2，因为否则 AI 最终会给你一大堆问题，而且它会把不止一个问题放在一个项目符号里，这样就很难用了。所以，你得习惯这种明确性，对吧？所以我只是……然后说剩下的。你做最好的判断。
我用完全相同的提示。我说你选你认为最好的。是的，因为我有点懒，对吧？那么，好吧。我们开始吧。所以，它会启动并开始创建这个 PRD。现在，我选择的是，你知道，我这边有一个任务文件夹。你把 PRD 放在那里。然后我们稍后会生成一个任务列表。
它说：“好的，太棒了。”嗯，我现在就创建它。它正在生成中。所以，那会在一分钟内发生。我实际上要快速切换到我的浏览器，因为我想向你展示一些其他有趣的事情。嗯，首先，这是我那个超棒的游艇俱乐部会员应用程序，我们刚刚编码完成的。现在，我们会把这个链接放出来，但这是包含这些提示的仓库。
我们会把那些给你们。但是有一个非常酷的开源工具叫做 Taskmaster。这是开源的，完全免费，它就像是我向你们展示的东西的增强版。所以它以命令行界面工具的形式运行。它很棒。它实际上对我来说功能太强大了。
我想要更少的功能，更少的控制，或者说更少的功能，更多的控制。所以这是一个很好的替代方案。那么，我将回到 Cursor。只是简单回顾一下我们目前为止的进展。所以你有一个 Cursor 规则文件，它为生成 PRD 提供规则和指令。你在代理模式下使用你偏好的模型生成 PRD。
那个 PRD markdown 文件会放在你仓库中的一个 tasks 文件夹里。现在我们要看看那个 PRD，并展示你如何通过它来构建东西。完全正确。好的。你是个好学生。所以这是我们的 PRD。如果你看过 PRD，它非常简单明了。嗯，我来给你看看。所以，你有功能需求，嗯，非目标，设计考虑等等。
对吧？我们回过头来看看，我们到底在做什么？我们正在做的是向大型语言模型清楚地说明我们想要完成什么。我觉得我自己和大家都会犯的最大错误就是，试图匆忙地处理上下文，没有耐心告诉 AI 它解决问题实际需要知道的信息。
而且我认为如果我们都能稍微慢一点，完成这两个步骤，反而会加快所有事情的进度，对吧？所以，我们有了一个 PRD。这里没什么高深莫测的东西。嗯，接下来，我们要做的是为此生成任务。那么，让我们根据这个 PRD 来生成任务。所以我，如果你在听，我现在回到了 Cursor，我正在……我正在引入生成任务的文件。
它叫做 generate tasks，然后我会说，嗯，请为……生成任务，然后我会标记这个 PRD。明白了。所以这可能是另一个规则，类似于你的“生成 PRD”规则，它解释了什么是任务以及如何去做，然后你给它 PRD 本身的上下文。完全正确。那么让我们快速看一下生成任务列表。
所以，目标是引导一个 AI 助手创建一个详细的、按部就班的任务列表。这就是我希望列表看起来的样子。这就是过程。你会看到它会问我几个问题，然后它会以 markdown 文件的格式输出任务。我必须问，你是怎么创建这些规则的？这些……这个提示写得很棒。
它结构清晰，表述明确。你是怎么擅长写这些指令提示词的？和你一样的方法。基本上，我尝试了几种方法，它们行不通。然后我变得更具体，当然，你知道，我让一个非常智能的语言模型帮我写了这个，然后我再编辑它。你知道，你总是会学到一些行业诀窍。
比如，我希望这个任务列表是 Markdown 格式的，而且我希望有复选框，这样我们就可以勾选掉它们。我的意思是，诸如此类的蠢事。所以，我最希望大家从这个节目中学到的是，你知道吗，没有人真正知道该怎么做这些事。唯一能搞明白的方法就是亲自上手，看看什么方法有效。
嗯，然后坚持使用一个你一直喜欢的模型。比如，我正在逐渐非常了解 Gemini 2.5 Pro，它擅长什么，不擅长什么。那么，让我们看看它擅长什么。好的。所以我刚才说，“请为这个 PRD 生成任务。”我标记了那个文件。嗯，我们正在使用一个推理模型。
所以我们看到思考的代币（tokens）飞快地闪过，所有这些思考可能会让你多花一点钱，但你能获得更多的可见性。你能学到更多，而且看起来它做得很好。阿门。是的。我觉得多花那 5 美分，这绝对值得。
嗯，所以在这个指令中，它的指令是给我一些基本任务，然后问我它们是否可以，然后告诉我继续。所以你会看到它说“准备生成子任务。回复‘go’以继续”。你知道，当我并排看这个时，我非常喜欢你的生成任务指令的一点是，你给了它一个非常明确的流程。
你得到这个文件，你做第一步，你得到下一个文件。它不完全是一个代理（agent），但它确实引入了这种类似代理的思维方式，即决策点在哪里，用户交互点在哪里，嗯，但更多的是在这种线性的、嗯，一步一步的聊天模式中。完全正确。我一直不停地向 Cursor 团队强调，为什么不把这个直接内置到应用的核心开发者体验中呢？我不明白为什么这不应该是使用 Cursor 的方式，他们一直说正在做。所以，你知道，还有
对于正在收听的人来说，当 Cursor 完成生成时，那种令人愉悦的声音。那是不是最棒的？我喜欢那个。我爱它。本集由 Notion 为您呈现。Notion 现在是您工作中无所不能的 AI 工具。借助新的 AI 会议记录、企业搜索和研究模式，您团队中的每个人都拥有了笔记员、研究员、文档起草员和头脑风暴者。
您的新 AI 团队就在您团队已经工作的地方。我一直是 Notion 的长期用户，并且在过去几周一直在使用新的 Notion AI 功能。我无法想象没有它们的工作。AI 会议记录改变了游戏规则。摘要准确无误，提取行动项目对于站会、团队会议、一对一会议、客户访谈，是的，还有播客准备都非常有用。
Notion 的 AI 会议记录现在是我团队工作流程中不可或缺的一部分。像 OpenAI、Ramp、Vercel 和 Cursor 这些发展最快的公司都在使用 Notion 来完成更多工作。通过在 notion.com/howiai 使用您的工作邮箱注册，免费试用 Notion 所有新的 AI 功能。所以，我们接受这些更改。
我们跳到这边来看看它做了什么，对吧？所以，哦哇，我知道。很有趣，不是吗？所以，你在这里看到的是相关文件。现在，这是我从 X 上的一个朋友那里学到的一个技巧，我的想法是，这应该能帮助大语言模型记住我们真正关注的是哪些文件。
即使我会在上下文中特别标记这些，嗯，我认为这很有用。我想简单提一下，我可能过多地将大语言模型拟人化了，但是因为它们是在网络上的人类输出上训练的，你知道，我相信我们需要给大语言模型正确的上下文，并尽可能地提供帮助，这样它们才能真正解决我们的问题，对吧？我完全同意。
我也对大语言模型非常有礼貌。我就是这样让人们工作的。我也会这样让代理们工作，对吧？所以你为什么不呢，你知道，像对待人一样对待代理？我就是这么想的。所以我同意。好的。然后我们得到了，你知道，相当详细的任务列表，对吧？我们有 1 2 3 4 个子任务。
有些我们甚至还有子子任务。然后我们将开始在此基础上进行迭代。工作原理是这样的：我还有另一个名为“任务列表”的规则，可能需要重命名，因为它不太合理。但这是迭代这些任务的说明。所以我来带你过一遍。
所以这是任务列表管理。这些是在 Markdown 文件中管理任务列表以跟踪进度的指南。任务完成时，我们希望一次只处理一个子任务。这一点非常重要，即不要试图一次完成所有任务。当你完成一个子任务后，立即将其标记为已完成，然后你会看到我说在每个子任务后停止，并等待用户的继续指令，然后这里只是更清晰一点，如果人们看到我摇头，那是因为我现在意识到我像个业余爱好者一样使用 Cursor。
我怀疑这有多好，我只是在以太中漂浮，说，你知道，03 带我飞吧，也许我对自己的产品管理技能过于自信，所以我把这一切都放到了聊天中，但这就是方法，朋友们，我告诉你们，注意听。我很感激。我的意思是，我认为我们俩的答案都是肯定的。我的意思是，有时你确实需要放手一搏，看看会发生什么，但我一次又一次地认识到，如果我不遵循这个过程，我最终会陷入某种困境，不得不回滚。
那么，我们继续。我现在正在做的是标记任务列表规则，它告诉 AI 如何行动，然后我会说，让我们开始，然后我会标记任务，任务列表，也就是……所以我们有一个 PRD，我们有一个任务列表，现在我们有一套知道如何处理任务列表并实际完成工作的规则。它确实如此。好的。那么，让我们开始。它会思考一下，它正在查看这个庞大的任务列表，所以它说的是，好的，让我们
从第一个子任务开始：定义 Prisma 模式的邮件营销活动。我将首先阅读现有的 Prisma 营销活动。好的，现有的等等等等，你知道，它只是在思考，然后，砰，它已经勾掉了 1.1。伴随着悦耳的声音。我有个问题。你是否把这一切都与 git 挂钩了？你是如何进行这里的变更管理的？是的。
所以我通常的做法是，在我完成其中一个项目符号，其中一个父任务之后，如果我觉得应用程序处于可工作的状态，我就会提交。是的。那时我会提交。明白了。如果我不这样做，我会在所有这些任务完成之前都不会提交。哦，哇。
嗯，你知道，这可能，你知道，是半天的工作。你，你知道，你会习惯于想，哦，好吧，如果我现在必须回滚，你知道，情况会有多糟？是的。然后我试着思考，我需要撤销什么？所以它说，嗯，好吧，我应该继续执行子任务 1.2 吗？然后我会说，嗯，是的。
有时候我真的很懒，我只说 Y，就是字母 Y。所以它会继续工作。现在，我们不需要把整个过程都过一遍，但我只是想向所有优秀的听众和观众展示，这是一个非常容易遵循的过程，我用这个方法构建了庞大的功能。我的意思是，你知道，一万行代码，可靠地，几乎从未出过问题。
我仍然觉得这种人在回路中的部分非常重要，即在每个任务之后，你都要检查发生了什么。我注意到它经常会引入一些小问题，或者存在一个代码风格检查器（linter）的错误，然后你就得去修复它。所以，你知道，这对于完成实际的工程工作来说很棒。
但如果我退一步，对于那些不知道从哪里开始使用 Cursor 的产品经理来说，即使你只做了 PRD 任务列表部分，我现在看着这个任务列表，它基本上包含了史诗（epics）和任务。而这正是许多工程师和产品经理陷入困境的地方，比如谁来接手这个 PRD，并将其分解成在我们代码库中可行的正确步骤。
所以即使只是这样，对于构建产品的人来说，我认为也极大地节省了时间。所以现在你可以过度设计这个流程。你知道这实际上只是一个 markdown 文件。嗯，它有点像手动操作。你知道，我想，哦，也许我会用，你知道，Asana 的 MCP，你知道，服务器，然后创建 Asana 任务。然后，是的，我就想，不，像这样，对我来说，直接看一个 markdown 文件，知道发生了什么，甚至可以往里面添加任务，其实更容易。
所以，我鼓励大家从小处着手，从简单开始，熟练掌握并适应它，然后你就可以，你知道，更上一层楼了。所以，在 30 声提示音之后，我们将得到一份关于你的游艇俱乐部的报告。没错。邮件。我的意思是，说到 MCP，你在 Cursor 的体验中完全使用 MCP 吗？它是如何融入你的工作流程的？我是。
好的，我现在就给你们展示几个。嗯，一开始我用得不多，然后我开始使用的第一个 MCP 是针对 Postgres 的，因为它非常有用，可以问 AI：“嘿，你能去看看这个数据是否真的在数据库里吗？” 嗯，所以我从那个开始，然后就一发不可收拾了。
所以我现在给你们看几个我认为非常有用的 MCP 服务器。让我来分享我的屏幕。好的。所以你们现在看到的是，左边是 browser base。那是实际的后端。我有一个免费账户。所以，我只是想试试这个，看看效果如何。我要向你们展示一下，用 MCP 可以做些什么有趣的事情。
所以，在右边，我有 Cursor，我正在我的 Cursor 设置里。我要去 MCP，你会看到下面我有一个基于浏览器的 MCP。是的。我已经设置好了。我还有 Stagehand，也很有趣。那么，让我向你展示一下它是如何工作的。所以，我们回到开始一个新的聊天，打开代理模式，然后我会说，嗯，导航到 Chat PRD 并截屏。
现在，让我们看看 MCP 之神是否合作，因为我今天早上做过这个，然后我当时就觉得，“哇，这太酷了。”好的。所以，我们应该很快就能在这里看到，让我刷新一下看看。它成功了。哦我的天，这太酷了。好的。所以，在云端。哦。所以，我正在从 Cursor 控制云端的一个无头浏览器。
未来已来。这太疯狂了。好吧，这有什么用呢？这挺酷的。我的意思是，我们来做点好玩的事吧。所以，我会说，导航到定价页面。好的。所以我们应该能看到光标在这个云端的无头浏览器里移动。你这纯粹是在炫耀。
我……我今天早上刚学会这个，然后……好的。我当时就在想：“天啊，这太棒了。我们看看它到底能不能用。”是的，它动了。哇。所以再次强调，除了像个小把戏之外，这到底有什么实际用途？我认为它将为我解锁大量的前端测试。是的。
对。所以如你所知，现在尝试修复前端的 bug 非常笨拙，你得截屏，然后粘贴到 Cursor 里，然后说，不，我指的是那边，是的。嗯，所以现在我们有了这种能力，可以直接在 Cursor 内部开始自动化一些这样的行为。
那是我觉得很有趣的一个小技巧，想分享给你们。现在，如果你正在看这个，你会想，这到底是什么，我们在这里做什么呢……我会回到 Cursor 的 MCP 部分。我们基本上是在赋予 Cursor 与其他应用程序交互的能力，对吧？所以我们在说，好吧，Browser Base 很酷。
你可以在云端拥有这个浏览器，可以用它做很多事情。我希望能够告诉 AI 该做什么。我不想必须知道如何调用 API 以及做所有那些事情。所以，这就是我觉得非常有趣的地方。那么，你能带我们过一遍几个用例吗，就是我们在这里看到的这些？所以，Browser Base 基本上可以让你浏览网页，并通过 Cursor 的文本窗口做一些事情。
你觉得其他哪些比较有用？嗯，Postgres 可能是我用得最多的。嗯，所以对于我正在构建的初创公司，我正在 Vercel 上使用 Postgres 作为数据库。很多时候我只是想告诉 AI，你知道，这个值是否在数据库的这一行里？我不想必须自己写 SQL 来做这件事。是的。
所以你可以直接进入聊天窗口说：“嘿，你知道，用 Postgres 工具告诉我这个东西在不在数据库里”，这非常酷。Prisma 是我用在这个有趣的小项目上的。SQLite 也是一样。所以我每天都用的是 Postgres。我特别喜欢 AI 的一点，尤其是在这种开发者工具栈中，就是它减少了繁琐的工作。
我觉得对工程师来说，其中一件繁琐的事情就是为了协调跨任务列表的工作，你需要打开多少个标签页。对吧？你必须打开你的项目管理软件，这样才能知道你正在处理哪个任务，下一个任务是什么。你必须打开你的浏览器，这样才能做一些工作。
查询你的数据库，而这一切都把它整合到一个单一的界面中，你可以通过自然语言无缝切换。阿门。所以我确实想向你展示另一个非常小巧快捷的工具，我知道我们快没时间了。不，我们来吧。嗯，所以你可能一遍又一遍地听到所有正在收听或观看的人在强调上下文。
嗯，它比我们想象的要重要得多。再说一遍，如果我们拟人化地看，就好比，除非你给某人正确的书或正确的纸张，否则你怎么能期望他们做任何事情呢？所以，我开始使用一个叫做 Repo Prompt 的工具。再说一遍，我没有任何经济上的理由推荐这个。
我不拥有这家公司的任何股份，但它是一款非常棒的 Mac 工具。我只想快速向你展示如何使用它。问题是为什么？比如，为什么我不直接用 Cursor？嗯，Cursor 的问题在于，后台有很多关于上下文的神奇操作，除非你标记它，否则你无法确定上下文中到底有什么，对吧？这没问题，但它们有点把那个神奇化了，有时你真的非常想控制上下文。
所以，我打开了 Repo Prompt。左边是网站，右边是用户界面，我已经打开了“How I AI”项目，就是那个简单的游艇邮件应用。好的。假设我想把很多这些东西放到一个提示里去做某件事。所以如果我选择整个仓库，我们到下面的“撰写”这里。
你可以看到那有多少个 token。好吧，那是 395,000 个 token。好的，那太多了。那么，我们来减少一些。所以，好吧，我知道我想要 app 文件夹里的一些东西。好吧，为什么它这么大？现在，我们有，你知道，324,000 个。还是太大。所以，我想可能是在 generated 文件夹里。
所以，把它去掉。好的，很好。现在我们有 12,000 个 token。所以，重点是如果你知道你想要什么上下文，Repo Prompt 是一个非常强大的方法。所以，我要选择 components lib。嗯，让我们打开 Prisma 的 schema，你知道，还有 scripts。好的。现在呢？所以，我到这里输入一个提示。
我怎样才能提高这段代码的可维护性？然后你还可以做一些其他很酷的事情，比如我想包含一个存储的提示，我将使用架构师版本。那么，这是什么呢？嗯，这是一个 Repo Prompt 已经写好的提示，里面有很多类似高级技巧的东西，对吧？它应该像一个架构师而不是一个开发者那样行动。
现在我向下滚动到“复制”，然后我会说我想包含保存的提示，我想包含文件，我想包含用户指令。然后我点击“复制”。现在呢？嗯，我来给你看。那么让我们转到 03（Claude 3 Opus）。现在，我可以转到 Cursor，但我不知道 Cursor 会对我的上下文做什么。
相反，我会把它粘贴到这里。那么，这是什么？你所拥有的是，我们从底部开始看。它基本上是把所有东西都放在 XML 标签里，对吧？所以，你有用户指令。那就是我输入的提示。然后，元提示是告诉它如何执行提示以及它包含的文件。我们往上看。
所以，文件内容。这有点像关键。所以每个文件都被包含在内，并且它被特别地这样标记。所以它说的是带有实际路径的文件，它基本上非常具体。它告诉 AI 这正是你需要的上下文。它非常清晰，然后你就可以对它执行某种提示了。
我用这个来做一些繁重的工作，以前我经常用 01 Pro（可能是指 Claude 1 Pro）做这个，我会进入 Repo Prompt，我会精确选择正确的上下文，然后我会进入 01 Pro 说，好好思考这个问题。我已经给了你完全正确的上下文，因此你会得到非常棒的答案。是的。
而且这些新模型拥有如此大的上下文窗口，但我不会把 12000 个 token 复制粘贴到聊天窗口里，对吧？但这个工具就能帮你做到，非常有趣。然后你就少了一个黑匣子。是的，少了一个黑匣子。而且，你知道，我感觉这些东西可能最终会消失。
我想，你知道，上下文窗口会变得越来越大。像 Cursor 这样的工具在管理上下文方面会变得越来越好，你知道，但现在你必须做所有这些事情。比如你不能只是挥挥魔杖就指望大语言模型能为你写出所有正确的东西。我就是这么做的。
所以现在我有一个更好的流程可以遵循了。好的。在你的技术栈中还有其他非常重要的东西需要告诉我们吗？嗯，最重要的事情，嗯，是这个。让我分享我的屏幕。这是 Tiesto 在纽约的现场演出。所以这实际上是我编码的方式。
我只是打开一些电子舞曲，然后在深夜，你知道，等大家都睡了之后，听着超棒的电子舞曲编码。那是我技术栈中重要的一部分。所以如果我可以要求一个额外的 Cursor 功能，那就是 AI 生成的流媒体电子舞曲，与你的 token 生成速度相匹配，并且在结束时有一个 drop（高潮骤停），而不是那个 Cursor 的提示音。
我愿意为此多付钱。你可是第一个听说的。把所有这些任务管理、上下文窗口之类的东西都忘了。我想要的是乐趣。好的，我们花三分钟进行快速问答。第一，你是一个建设者。你是一个创始人。很明显这项技术正在改变建设方面的事情。
在你看来，它如何改变公司和创始人方面的事情？哇。这完全是重写。我很幸运地创办了三家公司，并看到它们被收购，其中一家，你知道，我们大约有 110 名员工，我有一个首席技术官和一个工程副总裁，还有产品经理，而且，你知道，创办这家新公司，我真的觉得自己能够完成所有这些工作。现在，我能像一个专门的产品经理那样做得好吗？不能。
我能像首席技术官那样深入思考吗？不能。但我确信能够独自建立这家公司。我的意思是，你正在用 ChatPRD 做这个。我的意思是，这竟然可能，这太疯狂了。所以，我简直等不及未来了。我完全同意。好的。然后，当然，你向我们展示了你是一个非常深思熟虑和有条理的产品经理，也是你的 AI 的管理者，但是你知道，它有时不听话。
那么，你怎么办？你有什么策略能真正让它回到正轨？我就是太好人了。我只是说，请再仔细想想。我知道你能做到。嗯，你知道，再想想。我不是个刻薄的人。所以，尽管我很想说，你知道，该死的，你知道，我不……我有一个假设，这其实是育儿方面的事情，因为我也做同样的事情。
我说：“我相信你能做到。我相信你。”我相信你能行。我相信你能行。那太棒了。瑞安，我们在哪里可以找到你，我们能帮上什么忙？嗯，我一直在 X 上。所以，x.comarson。嗯，如果你想了解更多，就去 rycarson.com，那就是我。太好了。非常感谢。
