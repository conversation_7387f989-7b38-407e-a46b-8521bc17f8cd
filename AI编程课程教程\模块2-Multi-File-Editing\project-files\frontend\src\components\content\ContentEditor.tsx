import React, { useState, useEffect, useCallback, useRef } from 'react';
import ReactQuill from 'react-quill';
import 'react-quill/dist/quill.snow.css';
import { useForm, Controller } from 'react-hook-form';
import { toast } from 'react-hot-toast';
import { Save, Eye, EyeOff, Clock, AlertCircle } from 'lucide-react';
import { Content, Platform, ContentType, ContentStatus } from '../../../shared/types';
import { useAutoSave } from '../../hooks/useAutoSave';
import { useRealTimeCollaboration } from '../../hooks/useRealTimeCollaboration';
import { contentApi } from '../../services/contentApi';
import { Button } from '../common/Button';
import { Modal } from '../common/Modal';
import { Loading } from '../common/Loading';
import { TagInput } from '../common/TagInput';
import { PlatformSelector } from './PlatformSelector';
import { ContentPreview } from './ContentPreview';

interface ContentEditorProps {
  content?: Content;
  onSave: (content: Partial<Content>) => Promise<void>;
  onCancel: () => void;
  loading?: boolean;
  mode?: 'create' | 'edit';
}

interface FormData {
  title: string;
  description: string;
  body: string;
  platform: Platform;
  contentType: ContentType;
  status: ContentStatus;
  tags: string[];
  scheduledAt?: Date;
}

export const ContentEditor: React.FC<ContentEditorProps> = ({
  content,
  onSave,
  onCancel,
  loading = false,
  mode = 'create'
}) => {
  const [isPreviewMode, setIsPreviewMode] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [wordCount, setWordCount] = useState(0);
  const [readingTime, setReadingTime] = useState(0);
  const [showScheduleModal, setShowScheduleModal] = useState(false);
  const editorRef = useRef<ReactQuill>(null);

  // 表单管理
  const {
    control,
    handleSubmit,
    watch,
    setValue,
    formState: { errors, isDirty }
  } = useForm<FormData>({
    defaultValues: {
      title: content?.title || '',
      description: content?.description || '',
      body: content?.body || '',
      platform: content?.platform || 'xiaohongshu',
      contentType: content?.contentType || 'post',
      status: content?.status || 'draft',
      tags: content?.tags || [],
      scheduledAt: content?.scheduledAt ? new Date(content.scheduledAt) : undefined
    }
  });

  // 监听表单变化
  const watchedBody = watch('body');
  const watchedTitle = watch('title');

  // 自动保存功能
  const { saveStatus, lastSaved } = useAutoSave({
    data: watch(),
    onSave: async (data) => {
      if (content?.id && isDirty) {
        await contentApi.updateContent(content.id, data);
      }
    },
    delay: 2000,
    enabled: mode === 'edit' && !!content?.id
  });

  // 实时协作功能
  const { collaborators, changes } = useRealTimeCollaboration(content?.id || '');

  // 计算字数和阅读时间
  useEffect(() => {
    if (watchedBody) {
      const plainText = watchedBody.replace(/<[^>]*>/g, '');
      const words = plainText.trim().split(/\s+/).length;
      setWordCount(words);
      setReadingTime(Math.ceil(words / 200)); // 假设每分钟200字
    } else {
      setWordCount(0);
      setReadingTime(0);
    }
  }, [watchedBody]);

  // Quill编辑器配置
  const quillModules = {
    toolbar: [
      [{ 'header': [1, 2, 3, false] }],
      ['bold', 'italic', 'underline', 'strike'],
      [{ 'color': [] }, { 'background': [] }],
      [{ 'list': 'ordered'}, { 'list': 'bullet' }],
      [{ 'indent': '-1'}, { 'indent': '+1' }],
      ['link', 'image'],
      ['clean']
    ],
    history: {
      delay: 1000,
      maxStack: 50,
      userOnly: true
    }
  };

  const quillFormats = [
    'header', 'bold', 'italic', 'underline', 'strike',
    'color', 'background', 'list', 'bullet', 'indent',
    'link', 'image'
  ];

  // 处理表单提交
  const onSubmit = async (data: FormData) => {
    try {
      setIsSaving(true);
      await onSave(data);
      toast.success(mode === 'create' ? '内容创建成功' : '内容更新成功');
    } catch (error) {
      toast.error('保存失败，请重试');
      console.error('Save error:', error);
    } finally {
      setIsSaving(false);
    }
  };

  // 处理发布
  const handlePublish = async () => {
    const data = watch();
    try {
      setIsSaving(true);
      await onSave({ ...data, status: 'published' });
      toast.success('内容发布成功');
    } catch (error) {
      toast.error('发布失败，请重试');
    } finally {
      setIsSaving(false);
    }
  };

  // 处理定时发布
  const handleSchedulePublish = async (scheduledAt: Date) => {
    const data = watch();
    try {
      setIsSaving(true);
      await onSave({ 
        ...data, 
        status: 'scheduled',
        scheduledAt 
      });
      toast.success('内容定时发布设置成功');
      setShowScheduleModal(false);
    } catch (error) {
      toast.error('设置失败，请重试');
    } finally {
      setIsSaving(false);
    }
  };

  // 切换预览模式
  const togglePreview = useCallback(() => {
    setIsPreviewMode(!isPreviewMode);
  }, [isPreviewMode]);

  // 键盘快捷键
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.ctrlKey || e.metaKey) {
        switch (e.key) {
          case 's':
            e.preventDefault();
            handleSubmit(onSubmit)();
            break;
          case 'p':
            e.preventDefault();
            togglePreview();
            break;
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [handleSubmit, onSubmit, togglePreview]);

  if (loading) {
    return <Loading message="加载编辑器中..." />;
  }

  return (
    <div className="content-editor">
      <div className="editor-header">
        <div className="editor-title">
          <h2>{mode === 'create' ? '创建内容' : '编辑内容'}</h2>
          {collaborators.length > 0 && (
            <div className="collaborators">
              <span className="collaborator-count">{collaborators.length} 人正在协作</span>
            </div>
          )}
        </div>
        
        <div className="editor-actions">
          <div className="save-status">
            {saveStatus === 'saving' && (
              <span className="saving">
                <Clock size={16} />
                保存中...
              </span>
            )}
            {saveStatus === 'saved' && lastSaved && (
              <span className="saved">
                已保存 {new Date(lastSaved).toLocaleTimeString()}
              </span>
            )}
            {saveStatus === 'error' && (
              <span className="error">
                <AlertCircle size={16} />
                保存失败
              </span>
            )}
          </div>

          <Button
            variant="outline"
            onClick={togglePreview}
            icon={isPreviewMode ? EyeOff : Eye}
          >
            {isPreviewMode ? '编辑' : '预览'}
          </Button>

          <Button
            variant="outline"
            onClick={onCancel}
            disabled={isSaving}
          >
            取消
          </Button>

          <Button
            onClick={handleSubmit(onSubmit)}
            loading={isSaving}
            icon={Save}
          >
            保存草稿
          </Button>

          <Button
            variant="primary"
            onClick={handlePublish}
            loading={isSaving}
            disabled={!watchedTitle || !watchedBody}
          >
            立即发布
          </Button>

          <Button
            variant="secondary"
            onClick={() => setShowScheduleModal(true)}
            disabled={!watchedTitle || !watchedBody}
          >
            定时发布
          </Button>
        </div>
      </div>

      <div className="editor-content">
        {isPreviewMode ? (
          <ContentPreview
            title={watchedTitle}
            body={watchedBody}
            platform={watch('platform')}
            contentType={watch('contentType')}
            tags={watch('tags')}
          />
        ) : (
          <form onSubmit={handleSubmit(onSubmit)} className="editor-form">
            <div className="form-row">
              <div className="form-group">
                <label htmlFor="title">标题 *</label>
                <Controller
                  name="title"
                  control={control}
                  rules={{ required: '标题不能为空' }}
                  render={({ field }) => (
                    <input
                      {...field}
                      type="text"
                      id="title"
                      placeholder="输入内容标题..."
                      className={errors.title ? 'error' : ''}
                    />
                  )}
                />
                {errors.title && (
                  <span className="error-message">{errors.title.message}</span>
                )}
              </div>

              <div className="form-group">
                <label htmlFor="platform">发布平台</label>
                <Controller
                  name="platform"
                  control={control}
                  render={({ field }) => (
                    <PlatformSelector
                      value={field.value}
                      onChange={field.onChange}
                    />
                  )}
                />
              </div>
            </div>

            <div className="form-group">
              <label htmlFor="description">描述</label>
              <Controller
                name="description"
                control={control}
                render={({ field }) => (
                  <textarea
                    {...field}
                    id="description"
                    placeholder="简短描述内容..."
                    rows={3}
                  />
                )}
              />
            </div>

            <div className="form-group">
              <label htmlFor="body">正文内容 *</label>
              <Controller
                name="body"
                control={control}
                rules={{ required: '内容不能为空' }}
                render={({ field }) => (
                  <ReactQuill
                    ref={editorRef}
                    theme="snow"
                    value={field.value}
                    onChange={field.onChange}
                    modules={quillModules}
                    formats={quillFormats}
                    placeholder="开始创作您的内容..."
                  />
                )}
              />
              {errors.body && (
                <span className="error-message">{errors.body.message}</span>
              )}
            </div>

            <div className="form-group">
              <label htmlFor="tags">标签</label>
              <Controller
                name="tags"
                control={control}
                render={({ field }) => (
                  <TagInput
                    value={field.value}
                    onChange={field.onChange}
                    placeholder="添加标签..."
                  />
                )}
              />
            </div>

            <div className="editor-stats">
              <span>字数: {wordCount}</span>
              <span>预计阅读时间: {readingTime} 分钟</span>
            </div>
          </form>
        )}
      </div>

      {/* 定时发布模态框 */}
      <Modal
        isOpen={showScheduleModal}
        onClose={() => setShowScheduleModal(false)}
        title="定时发布"
      >
        <div className="schedule-modal">
          <p>选择发布时间：</p>
          {/* 这里可以添加日期时间选择器组件 */}
          <div className="modal-actions">
            <Button
              variant="outline"
              onClick={() => setShowScheduleModal(false)}
            >
              取消
            </Button>
            <Button
              variant="primary"
              onClick={() => handleSchedulePublish(new Date())}
            >
              确认
            </Button>
          </div>
        </div>
      </Modal>
    </div>
  );
};
