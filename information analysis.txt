information analysis信息分析基础
1. 信息分析框架
信息分析框架，是信息分析质量和效率的关键影响要素之⼀，另⼀个是信息源。
时空变量，就是⼀个底层通⽤的信息框架。不同领域的信息分析，都可以应⽤。
1、时间维度
可以参考时间单位，看不同时间量级下的变化或者差异。⽐如，分、⼩时、⽇、周、⽉、季度、年、世纪等。
可以在某个时间单位下，在时间轴上，给时间分段，看不同阶段的变化或者差异。⽐如，天，昨天、今天、明天；
季度，1季度、2季度、3季度、4季度。
可以在某个时间单位下，不同时间轴上，看时间分段的变化或者差异。⽐如，2021年的1、2、3、4季度 vs 2022
年的1、2、3、4季度。
2、空间维度
可以从地理看，⽐如：不同国家、省份、城市、城区、社区。
可以从机构公司等群体组织看，⽐如：学术机构、企业组织。
可以从虚拟空间看，⽐如：不同平台（抖⾳、⼩红书、快⼿、微博、⾖瓣、知乎、B站）。
3、变量关系
变量是复杂和多样的，如果⽆限拆解和扩展下去，就会失去框架的意义，迷失在⽆⽌境的信息分析的汪洋中。我们
借助回答3个关键问题，找到关键的变量和这些关键变量间的关系。
① 定义，是什么，通过界定它⾃⼰去理解。
② 分类，它属于谁，它包含谁，通过包含关系去理解。
③ 影响，它和哪些因素相关，它影响了什么，它受什么影响，这个变量和相关因素间有什么确定的因果关系吗，通
过相关与因果关系去理解。
2. 信息源
好的信息源，可以提⾼信息获取的质量和速度。信息源的优劣，可以推开你知道和不知道之间的那堵墙。
信息源的信息源，就是提速的提速，提效的提效。信息分析⼯具箱就相当于是信息源的信息源，类似这种的
awesomelist，就是当我们不知道有什么优质信息源的时候，就去这些信息源的信息源搜索查找。
同理，预习课程中的zotero的translator，也是⼀个信息源的信息源吧。
3. 借信息的度量理解信息
1、信息是动态的适应过程。⾸先，是过程，是未知到已知、不确定到确定、不断进化和适应的过程。
其次，是适应不是预测。适应，就意味着有预测误差（prediction error ，我发现翻译成预测误差，⽐如预测错
误，我⾃⼰理解起来更容易⼀点）。⾯对误差，我们循环调整感知和⾏动，越来越适应，不断缩⼩误差。这是⼀种
应对⽽⾮预测。
参考⾃由能的⾃发进⾏和平衡状态的概念。向⾃由能减⼩的⽅向⾃发进⾏直到平衡状态，就是⼀种适应的过程，就
像是热⽔适应室温变成常温⽔⼀样。温度差的不断缩⼩，就是预测错误的不断缩⼩。
热⽔⾼⽔温的内部状态（internal state）通过不断循环的感知和⾏动，适应更低温度的外部状态（external
state）。
预测误差和预测，逻辑不同。
预测误差有⼀个不确定的预期，可能会发⽣，但不知道概率。这个预期是⼀种内部感知，根据外部情况在改变（进
化）。
预测有⼀个确定的预期，前置了确定的发⽣概率。问题在于，现实世界不可能都能提前知道会发⽣什么、概率多
少。
2、信息的载体，aka 媒介，也是信息。
信息受⽣物和环境的影响，同时，⽣物、环境作为信息的载体，本身也是信息。我们⼈接触到的信息，是“主观
的”，因为信息已经受到了我们⾃⼰的影响。
在信息、⼈、环境三个关系中，⼈处理信息也会受到环境的影响，环境和⼈⼜受到信息的影响。这三者是相互影响
的，我们之后做信息分析，需要注意这个现象。
借此，信息与⼈的关系，正好对应了信息时代四种⼈。利⽤信息的⼈是套利者，被信息利⽤的⼈是⾲菜。
3、从上⾯两点看，信息既是⽆处不在的，⽽且信息的基础上会增加信息，信息⽣⽣不息。似乎信息本身，是没有
边界的。
如果信息本身没有边界，本来⽤来减少不确定性的信息，现实⽣活中，⼀不留神就会反作⽤，增加不确定性。如果
要通过信息分析，减少不确定性，帮助决策，我们就需要主动建⽴信息分析的边界。
4. 信息分布和传播的启发
1、根据信息分布特点，选择适合的套利信息，明确套利预期。
正态分布，⼩玩家也有机会，如果没有绝对优势，但可以在某个⼩维度找到相对优势，就可以参与。套利预期不要
太⾼。
幂律分布，尾部努⼒⼤于回报，如果没有绝对优势，做不到头部，不要参与。如果有，就参与并且加强头部效应，
享受更⾼套利回报。
2、借⽤信息传播模型，设计信息套利的策略。
巴斯模型，参考模型，判断某个信息套利机会，处在哪个阶段，是否可以⼲扰某种采纳率来获益。
德格鲁特模型，如何利⽤模仿和社会影响，在营销⼯作中，影响消费者的信息传播，借信息传播影响消费者观点和
⾏为，尤其在现在的社群、社交媒体、兴趣群体聚集的环境下。
博弈，信息套利中，从博弈的⻆度理解⼈们的已有⾏为和潜在⾏为，把可能的博弈情况，考虑进去。什么样的博弈
是⼈性使然的⼤概率，是否可以顺应或者避免。三⼤模型
1. 全局认识
1.1 参考前⾯的课程，理解信息的全局认识
“信息（分析）”，作为全局认识的全局认识，对照四个标准：
1. 深刻⻅解：帮助理解⽣物场、信息场、物质场中信息的分布、传播等规律特征，理解对应世界的运转机制。
2. ⾏动：1中的这些深刻⻅解，可以帮助我们采取具体⾏动，⽐如⽣活中⽇常购物、职业决策、投资⾏动等。
3. 机会：信息是⽆处不在的，对信息（分析）的认识，能够应⽤在不同的场合中，以不同形式建⽴理解和⾏动。
4. 伦理道德：通过3中的反复和不同层次递进，对信息的认识不断内化，可以规范我们的⾏动、改善我们的道德
观念。
为建⽴“信息”的全局认识，借助了三个维度的最⼩全局认识。
1. 信息的基本参数：信息的度量（⽐特、⾃由能）、信息的基本框架（时空变量）
2. 信息的神器清单：信息分析⼯具清单、信息源清单、信息搜索技巧清单
3. 信息的协议接⼝：信息交互形成的分布和传播特征。
1.2 全局认识的意义
1. 借⽤4个标准，帮助理解⾯前的知识，筛选排序⾯前的知识，时间精⼒分配在更有价值的地⽅
2. ⾏动前，问⼀句，（最⼩）全局认识做了吗？作为⾏动的检查标准，避免盲⽬⾏动。
3. 信息分析前，问⼀句，怎样就算（最⼩）全局认识？作为分析思考的检查标准，避免陷⼊分析陷阱，⽆法⾏
动。
1.3 信息分析的全局认识
关键是⽹络视⻆。
⾯对的物质存在、⽣物存在、信息存在的信息，都不是单⼀独⽴的存在去看，⽽是以⽹络的存在去理解。
这意味着“整体视⻆”和“关系视⻆”。
⽐如之前学习信息的时候，理解信息的分布和传播。信息的分布，意味着⽹络整体呈现的某种形态特征、意味着这
个⽹络有⼀个边界。信息的传播，意味着有来源有去向、意味着这个信息⽹络有源头和路径。有传播，意味着这个
存在是动态的、过程的。即使是分布，也是特定时点的静态特征。
整体性理解+动态，意味着信息分析中的全局认识，不需要绝对精确的答案。
参考⾃由能概念，通过反复的、层层递进的全局认识，降低预测错误。建⽴每次全局认识的时候，我们先追求满意，⽤“代表”来理解。抓⼤放⼩，平衡效率和质量的关系，找⼤概率能代
表主要特征的信息。先对信息⽹络整体有⼀个基础认识，然后根据实际需要迭代，在层次上递进。
这个基础认识程度的把握，就需要先划定⽹络边界。
⾄此，就有了构建全局认识的四个关键：理解边界→找代表→求近似解→升华层次。
两个注意：
⼀，即使是近似，也尽量避免偏⻅，所以我们需要交叉验证。
⼆，要做好层次升华，就需要每次圈定⾃⼰的近似解处在什么位置（现在认识的层次），还可以往哪⾥⾛（存在的
层次关系）。全局认识强调不断重复（机会），这个重复的过程中，我们可以尝试向源头推进。⽐如，参考“元”思
考⽅式，多问问受什么影响，以什么为基础。
1.4 最⼩全局认识
建⽴全局认识，是从⽹络边界出发，找代表性的信息。这些关键的代表性信息，就可以理解为最⼩全局认识。他们
的特点是，信息量更⼤。
代表性信息，⽐如，学科中的“最具影响⼒”的期刊，借代表性的期刊，建⽴学科的最⼩全局认识。
再⽐如，期刊中的代表性的机构、作者、主编、主题等。我理解，我们做的期刊的全局认识，就是先建⽴最⼩全局
认识，了解这个期刊的代表性的机构、主编、作者、主题等。然后把这些最⼩全局认识联通，串联起来理解整个期
刊⽹络。
找代表性信息，就是找到代表性的维度，然后在这个维度中找代表性信息点。⽐如，前⾯学习的信息分析框架，时
空变量，就是⼀个通⽤的模型，帮助我们找“代表性”信息维度。
再⽐如，充电头的案例，我们借⽤电商⽹站或者说明书，找到基本参数，这些参数就是代表性的维度。然后通过国
标、其他信息，找到功率这个代表性维度中，具有代表性的数值65W。
代表性，本来就是相对的。现在这个层级的分析，它可能是最⼩全局认识，当我们向下拆解到更细的维度，可能就
变成了全局认识。往下找到的关键节点，就成了最⼩全局认识。
1.5 常⻅的最⼩全局认识
常⻅的“代表性信息”来源，有三类：基本参数、协议接⼝、神奇清单。
基本参数，可以从单位和模型⼊⼿。单位延伸出量纲思维，可以帮助我们做全局认识的层次升华。单位可以从基本
单位开始。模型，可以从通⽤模型（⽐如时空变量）、专业领域的常⻅模型（⼤五⼈格、SWOT、3C）开始。
协议接⼝，关于交互的约定和连通。我们就看某个情境下，是谁和谁在交流互动，这个交流是通过什么⽅式进⾏
的，交流中做了什么样的约定。
神奇清单，分通⽤和专业，关键是保持更新。
选择适合的类型建⽴最⼩全局认识，需要先理解信息⽹络边界。⽐如，1. 多⼤的信息量，
2. ⼤概怎么分布的（⽐如，多少类，每类多少信息量），
3. 有什么传播特点（⽐如，信息⽼化速度）。
1.6 期刊信息分析作业与最⼩全局认识
参考第⼗期的标杆作业，
1. 整个报告的信分框架，对应基本参数。
理解⼀个期刊，需要哪些代表性维度（学者、主题、机构），这些代表性维度中，有哪些具体代表性信息（不
同时间段的关键学者、不同主题下的代表性关系、关键学者的机构）。
2. 其中罗列的书籍清单、⾼被引作者列表、⾼被引⽂章等，对应神奇清单。
3. 期刊和期刊中的⽂章，对应⼈⼈交互的协议。
1.7 （最⼩）全局认识在⼯作中的启发
1. 基本参数：参考⼈货场这个参数建⽴业务的全局认识。⼈对应消费者，货对应公司和竞争，场对应平台渠道。
通⽤模型为时空变量模型，消费者叠加JTBD模型，建⽴最⼩全局认识。
2. 协议：⾏业中的标准。
3. 清单：⾏业中的公司/⼈物榜单、品类清单、对标企业员⼯⼿册
2. 交叉验证
2.1 ⼀点理解
交叉验证的关键，是养成习惯。⼤脑本能偷懒，有了⼀个答案就算完事⼉了。我们平常获得了某个信息、认知或⾏
动，容易默认是对的，放⼤它的价值。信息分析的交叉验证，通过四个步骤中增加交叉验证的动作，提⾼准确度。
全局认识，做某个决策或者⾏动之前，问⼀句，全局认识了吗？
交叉验证，同理，获得某个信息、产⽣某个认知、决策某个⾏动之前，问⼀句，交叉验证了吗？
2.2 ⼀点⾏动
避免⼀根经，可以从追问开始。每次⼀问，还有其他的xx吗？反过来成⽴吗？
⽐如，
还有其他观点/假设吗？反⾯观点/假设也成⽴吗？
还有其他证据吗？有反⾯证据吗？
还有其他信息源吗？反向搜索会有什么结果呢？
信息分析的四个步骤，做了什么？还有其他⽅式吗？第⼆件可以做的事情，就是主动在⼯作⽣活中，找到问题场景，练习ACH和模糊综合评价法，记录分析过程，慢慢
熟练。
3. 有趣度
3.1 ⼀点理解
并不⼴为⼈知且有价值的事物，是有趣的事物。
全局认识⽤来提⾼效率，交叉验证⽤来提⾼准确度，有趣度⽤来套利。
有趣的事物，是有价值且不为⼈知。有价值，你知道，你才能“搬运”。这⾥有⼀个供需关系。
价值就是需求。传播程度就是供应量。价值程度/传播程度，约等于，需求/供应。越供不应求，越有趣。
价值的⾼低是相对的。别⼈需要的，就是有价值的。不同的别⼈需要的组合满⾜，价值就会放⼤。
传播度⼩，代表着供应缺⼝⼤。他需要但他没有，他想知道但他不知道，你有你知道，你可以供应给他。如果别⼈
也知道，别⼈也可以供应给他。越多⼈知道，越多⼈供应，供应和需求⼀样多，甚⾄供应超过需求，有趣度就降低
了，没有了。
关于价值，有三个规律：
时间上越早越好。从供需关系看，供需不平衡⼀定会⾛向动态平衡。越早越不平衡，价值空间越⼤。
空间上离源头越近越好。价值随着⼀层⼀层的传递，会递减，因为每⼀层都会试图获取部分价值。离源头越近，递
减越少。
变量上与趋势越同频越好。永远顺势⽽为。势可以放⼤机会价值，降低套利难度。出同样的⼒⽓得更⼤的收益，获
取同样的收益可以有更⼩的⼒⽓。更重要的是，不要逆势。不要逆势。不要逆势。
题外话，参考阳⽼师提到的2020年开始的新机遇，智能制造（芯⽚、机器⼈、⽣物制药、⾃动驾驶汽⻋ etc），我
似乎也应该基于此信分⼀个，思考如何顺应这个势能呢？
关于传播，参考⼩众、反常，从“⾼⻔槛”⼊⼿，从少有⼈想到、做到的事情⼊⼿。某种程度上，浑⽔的调查⽅法，
因为资⾦⻔槛，也算⼩众⽅法。尘光研究，有技术和认知⻔槛，能做到的⼈不⼀定能想到，能想到的⼈不⼀定能做
到，⾃然也是⼩众⽅法。
同理，获取特定领域知识的时候，⼤众畅销书和常⻅科普材料，就是⼤众的，即使很有价值，也不能“有趣套利”，
你知道的别⼈也知道，你看到的别⼈也看到，没有太⼤的供需缺⼝。3.2 有趣度计算
理论公式：价值程度/传播程度
数学公式：中介中⼼性/程度中⼼性
快速评估⽅法：2x2矩阵分类。价值=可能性x重要性，找到两个关键变量，打分乘积估算。锁定落在「⾼价值、⼩
传播」象限的信息。
3.3 有趣度注意事项
1. 价值⾼低判断，避免禀赋效应，客观辨别相对⾼低，不是我认为的⾼低，避免⾃嗨。
2. 要有窗⼝意识。在套利窗⼝期内⾏动。⾏动之前知道所在窗⼝期阶段。⼀个简单的判断⽅法：别⼈已经总结得
⾮常清楚的，⼀般机会窗⼝已经错过了。越是模糊不清的，可能窗⼝期还在。
3. 要有源头意识。往供需关系的源头⾛，往更⾼层次⾛，往底层知识⾛。持续追问，来⾃哪⾥？供应⽅的供应⽅
是谁？
4. 先⼩量级测试。
5. 交叉验证。选择不同学科、不同领域的知识都⽀持的。
信息获取
关键操作
1. 从⼀个信息源快速开始
1. 元信息源：A的A是什么？A会集中聚集在哪⾥？
2. 开源信息源
1. Awesome OSINT List：https://github.com/jivoi/awesome-osint
2. 开智信息分析⼯具箱：https://anrenmind.feishu.cn/docs/doccnuYPLJF2pj6bIeOOtyE45oe?from
=space_home_recent&pre_pathname=%2Fdrive%2Fhome%2F
3. 搜索引擎
4. 专业数据库
5. 访谈或调查
2. 快速验证内容是否符合需求：ctrl/cmd + F，简单，但⽇常没有习惯使⽤。
1. 全⽂搜索关键词，迅速验证检索的信息源是否符合需求。
2. 检索的关键词巧妙选择，海量信息中快速定位⾃⼰需要的
1. ⽂字标识符，时间（年、⽉、⽇）空间的（省、市、⼤学）
2. 形式符号，《》，@，·
3. 延伸的软件，⽐如⾃带的spotlint没有⽤起来，Alfred⽤得太简单了。Alfred的⼀些⾃动化功能，我应该
设置调教⼀下，提⾼⽣产⼒。3. 调整信息源
1. 更多
1. Similar web
2. AI推荐，⽐如academic influence
3. 搜索指数
2. 更好
1. 搜索引擎 → 专业数据库
4. 批量下载&智能获取，这块技能之前接触较少，⼯作其实经常可以⽤到，要尽快掌握。
1. 批量下载：
1. Zotero，Refworks是Zotero识别格式。
2. DownThemAll
2. 智能获取：
1. 爬⾍：webscraper.io、bazhuayu.com、houyicaiji.com、import.io
2. RPA：UiPath.com、cyclone-robotics.com、laiye.com
信息整理
获取信息后，⼤部分是不能直接加⼯和分析的，需要有序存储、数字化归档后使⽤。
1. 信息整理的关键起点
好的信息整理，保存时省时省⼒，调取时快速准确。21世纪的信息整理，以元数据为起点。
元数据重要类型：
① 描述性元数据
⽤于搜索发现和定位识别⽂件信息，⽐如标题、摘要、作者、种类、⼤⼩、分辨率。
可能这就是为什么，我们整理⽂件时，根据“种类”分类。Mac中的⽂稿、图⽚、⾳乐、影⽚、应⽤程序。
② 结构性元数据
描述⽂件如何构成的，⽐如书的⽬录，章节。
③ 管理性数据
管理⽂件留下的信息，⽐如，⽂件创建时间、修改时间、权限。
2. 信息整理的四个关键步骤2.1 提取元数据
信息获取时，批量提取⽹⻚信息，⽅便批量下载。信息整理时，批量读取本地⽂件元数据，⽅便利⽤元数据批量分
类。
① Zotero 提取论⽂元数据。
Zotero⾃动识别、借⽤插件识别。如果都⽆法识别，就只能借助DOI或者ISBN等元数据协议，⼿动完成。
② Calibre 提取图书元数据。
③ ExifTool 提取图像、视频、⾳频元数据。
2.2 数据清洗
去重的依据，是唯⼀编码原则，⽐如⽂献的DOI编码、⽂件hash值、图书ISBN码、数据库编号。⽂件名，也是重要
的唯⼀编码。
⼩提示，
① 数据清洗，主要包括：去重、删除、重命名、异常值&缺失值标记
② Zotero中，可以保持⼀定冗余，不⽤太在意去重，除⾮是具体⼯作中不允许重复。
③ 如果不知道怎么处理异常值、缺失值，就可以删掉处理。
⼩⼯具：
系统去重&批量删除：Mac⾃带、CleanMyMac、腾讯柠檬
Zotero去重&批量删除：⾃带、Duplicates Merger插件
重命名⼯具：Mac⾃带、MacOS 快捷指令、Zotero⾃带、Zotfile
2.3 分类与标签
关键，分类为主，标签为辅。
分类，基本属性为起点，不随时间动荡。
标签，记录印象，随意⽽⾮规整，随时间崩溃。
⼩技巧-四个原则：
① 以基本属性分类⽂档的结构⾮常稳固，不随时间变化。
② 把⽂档按树形结构整理
③ 复⽤模版，⽐如meta元数据、others⼤箩筐。
④ 智能整理，借⽤软件的智能搜索功能。妙招 ⼯具&操作
⼀键提取（元数据） Zotero、Calibre、ExifTool
批量清洗（去重、删除、重命
名） Mac⾃带、CleanMyMac、快捷指令、Automator
智能整理 软件⾃带智能检索、Mac智能⽂件夹、快捷指令、Automator、RPA技
术
应⽤到本地⽂件管理的⼩技巧：
① 命名，提⾼机器可读性，⽤英⽂（or拼⾳）、⽤名词。
② README⽂件和模版⽂件，⽅便沟通、协同、复⽤。
③ 层级符合“四的五次⽅”结构。
2.4 数据备份
关键不要污染原始数据，原始数据和处理后数据，要分开备份。避免处理错误，⽆法迅速回到原始数据重新整理，
避免重新花时间获取原始数据。
3. 三⼤妙招
4. 信息整理的实践
案例1：Alfred 快捷搜索
Alfred，导⼊github下载的workflow，完成了
Notion：直接⽤Alfred开始Notion内的搜索、新建note
搜索：加⼊了github快捷搜索，熟悉了各搜索引擎搜索
toggle：Alfred添加entry
flomo：Alfred添加note，但要API，会员服务。找到flomo正好和阅读app有合作，想试试看。会不会提⾼阅
读效率和习惯。
Zotero：没有调⽤成功，暂时不管。
Douban: 直接搜索⾖瓣的图书、⾳乐、电影。
以上，⽬前够⽤。不要过度沉迷，当我发现有什么快捷操作需求时，搜索解决⽅案，导⼊设置即可。
问题：电脑部分命令跑不了，可能是没有⼀些语⾔包。⽐如，php之类的。
案例2：MacOS ⾃带效率功能
1. Mac 智能⽂件夹：⽂件→新建智能⽂件夹
2. Mac Automator批量重命名：设置了⼀个批量重命名应⽤程序（⾃定义版），⽤Alfred调取，任意定义⽂
档，⾃定义重命名⽅式，⾃动批量运⾏。
3. 快捷指令，待体验。（猜想，和Alfred差不多？？）
4. Mac Automator⾃动种类分类：固定在⽇常存放新进⽂件的⽂件夹，需要时，批量拖⼊⾃动整理即可。
案例3：Calibre vs Neat
对⽐使⽤了Neat Reader和Calibre管理和阅读电⼦书。
1. 跨平台使⽤
NeatReader最⼤的好处是，跨端跨平台使⽤，⽅便移动阅读。
Calibre不提供安卓和ios版本。
2. 轻量阅读体验
Neat Reader更流畅，画⾯字体排版更舒服。Calibre可能⾃⼰设置后会好⼀点。
3. 元数据管理
Neat没有。
4. ⼤批量书籍管理
Neat⽆法做到Calibre的全⾯功能和信息批量获取。
5. 深度阅读和书中内容管理检索
Calibre更适合提取和创作，内部搜索更⽅便。⼤量⽂字内容也可以。
两个软件各⾃扮演各⾃的⻆⾊。轻量阅读娱乐的书籍，可以放在Neat⾥⾯，⽅便随机阅读。
所有书籍⽤Calibre管理，重度阅读，需要⼤量输⼊输出的，⽤Calibre完成。
5. 本周答疑
5.1 阳⽼师
1、WOS最严谨，Google学术最宽松，语义学者在中间。体现学术功⼒，⽤WOS。1998年数据之前，都会少⼀
点，如果研究古⽼的，尽量⽤WOS或者APA等⽐较古⽼的数据库。
2、信分过程和复盘的记录，以写给别⼈看出发。假设陌⽣⼈看，也能看明⽩你的检索路径。写给⾃⼰看，会省略
信息，导致三五年后，⾃⼰也看不懂。容易出错的，可以写备注。3、⼀般作者信息，⼤学信息是最准确的，官⽅的，最为严谨的。
4、H指数，对越古⽼的学者，代表性越差。可以调整搜索不同形式的名字，检查下。
5、论⽂摘要没那么重要。没有的，可以使⽤第⼀段前⾯的内容。摘要可以补⻬，也可以不补⻬。更快速的补⻬⽅
法，使⽤R语⾔写⼀个脚本。
6、zotero，⼀个项⽬本地做完之后，整理完善放到群组，⼀个项⽬放⼀个群组，⽅便管理。⽂献库更适合容纳我
们当前正在⼯作的项⽬。
7、不同学科的代表性作品类型，挑代表性作者、代表学校专业，查看罗列信息去验证。同理，商业信息，就看下
代表公司。领域中代表性的公司怎么做，往往代表了这个领域的规范。
8、结业⼤作业做完，会对我们的信息分析能⼒，有⼀个本质性的提⾼。学术领域相当于是⼊⻔。
5.2 北顾⼤叔答疑takeaway
1、数据源对⽐，发现Dimension⽐WOS元数据信息更全，对于某种期刊。但⽂献数量更少。
2、⼤叔也提到了Zotero魔法棒重新批量抓取。我⾃⼰操作，之前第⼀次成功了，后⾯没成功，是否可能因为VPN
设置，导致特定url翻墙失败？
3、判断摘要是否重要，⽐如，被引少、都是补充材料的类型，都是代表该论⽂不是重要论⽂。被引⾼的，补充⼀
下就好了。
4、批量处理信息，可以⾼级搜索重要标签，之后需要精读时，直接调取。
5、⾼级搜索参考：
⾼级搜索综述：标题包含review、meta、history 关键词。
⾼级搜索包含期刊名字的。
⾼级搜索Editorial的
6、WOS⾃带数据集，⽤bibilimetrix分析最不容易出错。
7、条⽬加了⼤量信息后，不适合⾃动批量merger了，有可能会把你想要merge的信息删除，除⾮测试发现，都能
够保留。
8、不需要下载每⼀篇PDF，多了也会崩溃的。信息加⼯后，再安排下载就好。所以，要提前关掉⾃动获取PDF的功
能和插件。
9、zotero移动：直接移动 vs 按住cmd移动。
信息加⼯
1. what
信息获取，是获取客观存在的数据。
信息整理，去掉数据中的噪⾳，保留或填补降低不确定性的信息，并将信息分⻔别类，⽅便加⼯。技巧 具体
检查证据
的诊断价
值
① 放弃似乎符合所有假设的证据。 ② 检查证据的准确性。 ③ 检查证据是否有不同解释。
检查假设 ① 检查假设的表述。 ② 检查是否需要添加新假设。 ③ 检查假设时是否受到未列⼊清单的证据
影响。 ④ 检查假设时是否受到不重要、⽆诊断价值证据影响。
证伪⽐证
实重要
① 优先寻找证伪证据、推论。 ② 从排除法开始。 ③ 不能否定的假设，先保留。 ④ 反驳最少
得假设，可能性最⼤。关注减号最少，⽽⾮加号最多。
结论不来
⾃矩阵 ① 关注证据和假设的联系，找出关键证据。
信息加⼯，将信息变为认知，把信息转化为个⼈知识，帮助认识世界。
2. how
2.1 ⼤胆假设，收集证据，⼩⼼求证 → 清单
1. ⼤胆假设，可以参考以下技巧：
试图反驳。尝试提出和第⼀反应相反的猜想，考虑开始认为不可能的猜想。⽐如，我不相信 → 那如果是真的
呢？我相信 → 那如果是假的呢？
考虑假设的“系统”、“基本场”、“世界”。⽐如，这个假设在什么系统、什么基本场、什么世界中，还有其他系
统、场、世界的假设吗？有什么跨（连接）这两个系统、场、世界的假设吗？
最少实体，解释更多，兼容更多证据。
保持质疑。对假设保持质疑，质疑假设，试图反驳，再质疑这个反驳。
2. 收集到的证据，参考以下维度，判断证据的价值和质量。越满⾜这些条件，证据的等级越⾼。
更多⼈验证
更多⼈使⽤
更⼤影响范围
更稳定、更少变动
3. 假设证据矩阵的技巧模
式
类
型
模
式
名
称
信息加⼯⽅向 案例
通
⽤
-
时
间
模
式
时
序
模
式
随时间变化，时间先后出现的趋势，
⽐如，增⻓、降低、循环。
⽜⼈代表作品出现时点、学科不同时期发展特点、⾏
业不同时期发展特点
通
⽤
-
空
间
模
式
地
理
模
式
具体的空间，地理位置上的分布特
点。
南张北⻬、学科以⾼校位置为代表的理论学派、⾏业
以公司位置为代表的产业带
⽐
例
模
式
抽象的空间，同⼀维度，局部与整体
的占⽐。包括正⽐、反⽐、指数、对
数⽐例。
⻩⾦⽐例、异步律、转化率、点击率
映
射
模
式
抽象空间，不同维度，从⾼维投射到
低维。
⼤五⼈格模型、Zotero信息管理降维、JTBD⽤户⽬标
模型
通
2.2 得出有边界的结论 → 单条结论
参考图尔敏模型，得出结论时，要考虑六⼤要素：证据、假设、结论、⼤前提、例外、限定。
有边界的结论，例外和限定，尤为重要。
查找反驳和限定的关键词技巧：
反驳：Rebuttals （反驳）Critics（批评）Criticism （批评）Challenge（挑战）False（错误）Objection
（反对）
限定：Quallifiers（限定）Limit（限制）Limitation（限制）Boundary（边界）
2.3 汇总与组织多条结论 → 模式
pattern，可参考的结构。⽤
-
变
量
模
式
⽐
较
模
式
数值、类别上的差异。包括相对差
异、绝对差异。类别变量需要借助中
介⽐较。常⻅⽐较：类别、等级、⽐
例、等⽐。
类别（性别、语⾔）、等级（服务评级）、⽐例（温
度、年份）、等⽐（价格、年龄）
分
布
模
式
信息分布的规律，⽐如，正态分布/偏
态分布、幂律分布
正态分布（身⾼、智商）、幂律分布（朋友数、资产
规模、销售额）
关
系
模
式
常⻅关系：从属的层次关系、影响的
数值关系、⽹络关系
层次关系（⽣物进化）、数值关系（线性回归、结构
⽅程模型）、⽹络关系（⼈际关系⽹络）
通
⽤
-
复
合
模
式
双
重
模
式
两种模式组合，⼀主⼀辅。
时序+地理：每500年，不同⼤洲GDP变化。 时序+⽐
较：每年，两个平台的GMV变化。最近10年，两个
关键词搜索趋势对⽐。
多
重
模
式
两种以上模式组合，⼀主N辅，多⻅
于专业模式。
专
业
-
学
术
模
式
学科的研究⽅法论（定性、定量）、
创新⻛格（原创、集展、⼯程）、学
派⻛格相关。
学派⻛格（奥地利 vs 芝加哥） 研究⽅法论（社会科
学常⻅研究⽅法论）
专
业
-
商
业
模
式
从价值链、消费者需求、公司管理出
发。
参考《利润模式》，从消费者需求和战略控制⻆度，
总结的22种利润模式。 参考《商业模式新⽣代》，
公司的商业环节出发，商业画布形式下的5种常⻅商
业模式。
专
业
- ⼈⽣发展、个⼈成⻓、婚恋、育⼉ 参考《⼈⽣模式》，⼈⽣周期、⼈⽣资本介绍。⼈
⽣
模
式
等。
实
践
策
略
⼯具 说明
整
理
清
单
⼤纲清单（树状）：workflowy、⻜书思
维⼯具、幕布 多维表格：⻜书多维表格、
Notion、Airtable
借助《开智信息加⼯模版》，提炼假设、证据，得出结
论。 不是必须按顺序填写。可能基于假设找证据，也可
能根据证据得出假设。
建
⽴
模
式
信息加⼯模版专业版、通⽤模式详图 要理解模式逻辑。
秘
密
武
器
bibliometrix（学术）、wind（商业-国
内）
bibliometrix使⽤的时候，⼀定要注意数据集完整、准确
性。
2.4 持续迭代 → 秘密武器
三个层⾯迭代：修正假设、增补证据、优化模式。
假设要避免认知偏差，参考步骤1中的技巧和检查清单。
证据客观存在，我们需要不断寻找挖掘更⾼价值的证据。
⽤秘密武器提⾼信息加⼯质量。⽐如，各个领域的专业分析⼯具、系统模型。
⽐如，学术信息分析⼯具的秘密武器，从citespace到bibliometrix。
说明
以上为开智学堂信息分析⼗⼀期学习笔记，核⼼内容援引⾃课程，版权归开智，附加我的个⼈理解，仅供参考。By
川⼦