// 抖音短视频文案模板
const douyinTemplate = {
    systemPrompt: `你是一位专业的抖音短视频文案创作者，擅长写带货和种草文案。

写作风格要求：
- 语言简洁有力，节奏感强
- 突出产品的实用性和性价比
- 字数控制在100-150字
- 使用短句，便于口播
- 包含热门话题标签
- 语气要亲切自然，有感染力

文案结构：
1. 强有力的开头hook
2. 产品核心卖点（3-4个）
3. 使用场景描述
4. 性价比总结
5. 相关话题标签`,

    userPrompt: `请为以下产品写一段抖音短视频文案：

产品信息：
- 名称：{productName}
- 类别：{productCategory}
- 目标用户：{targetAudience}
- 核心卖点：{keyFeatures}

要求：
1. 开头要有强烈的吸引力，制造悬念或惊喜
2. 用简短的句子列出3-4个核心卖点
3. 描述1-2个具体使用场景
4. 强调性价比和实用性
5. 结尾要有行动召唤
6. 包含3-5个相关热门标签
7. 语言要口语化，适合视频口播
8. 整体节奏要快，信息密度高

示例结构：
🔥开头hook！

这款{productName}真的太实用了！
✨ 卖点1
✨ 卖点2  
✨ 卖点3

使用场景描述！
性价比总结！

#标签1 #标签2 #标签3`,

    // 抖音热门开头模板
    hookTemplates: [
        '🔥爆款{productName}！',
        '这个{productName}绝了！',
        '终于找到好用的{productName}了！',
        '姐妹们，这款{productName}必须安排！',
        '不看后悔系列！{productName}',
        '真香警告！{productName}',
        '种草时间到！{productName}'
    ],

    // 抖音常用表达
    expressions: [
        '真的太实用了',
        '性价比绝了',
        '必须安排',
        '闭眼入',
        '不踩雷',
        '真香',
        '绝绝子',
        'yyds',
        '冲冲冲',
        '太香了'
    ],

    // 热门标签库
    popularTags: {
        electronics: ['#数码好物', '#黑科技', '#效率神器', '#数码测评', '#好物推荐'],
        fashion: ['#穿搭', '#时尚', '#好物分享', '#穿搭博主', '#时尚单品'],
        beauty: ['#美妆', '#护肤', '#变美', '#美妆博主', '#护肤心得'],
        home: ['#居家好物', '#生活好物', '#家居', '#生活技巧', '#居家必备'],
        general: ['#好物推荐', '#种草', '#实用', '#性价比', '#必买清单']
    },

    // 场景描述模板
    scenarios: {
        office: '上班族必备，办公室用太方便了！',
        home: '在家用着超舒服，生活品质直接拉满！',
        student: '学生党福音，宿舍神器！',
        travel: '出门旅行必带，太实用了！',
        daily: '日常使用频率超高，真的离不开！'
    },

    // 性价比表达模板
    valueExpressions: [
        '一个顶三个，性价比绝了！',
        '这个价格买到真的赚到了！',
        '同类产品中性价比最高的！',
        '便宜又好用，必须推荐！',
        '花小钱办大事，太值了！'
    ]
};

// 导出模板
if (typeof module !== 'undefined' && module.exports) {
    module.exports = douyinTemplate;
}
