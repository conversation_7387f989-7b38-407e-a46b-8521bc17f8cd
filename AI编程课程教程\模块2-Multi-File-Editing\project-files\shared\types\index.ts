// 共享类型定义文件
// 用于前后端数据结构的统一定义

// 基础类型
export type UUID = string;
export type Timestamp = Date;

// 平台类型
export type Platform = 'xiaohongshu' | 'taobao' | 'douyin' | 'weibo' | 'wechat';

// 内容状态
export type ContentStatus = 'draft' | 'scheduled' | 'published' | 'archived' | 'failed';

// 内容类型
export type ContentType = 'post' | 'story' | 'video' | 'live' | 'product_desc' | 'campaign';

// 用户角色
export type UserRole = 'admin' | 'editor' | 'viewer' | 'contributor';

// 用户接口
export interface User {
  id: UUID;
  username: string;
  email: string;
  displayName: string;
  avatar?: string;
  role: UserRole;
  isActive: boolean;
  lastLoginAt?: Timestamp;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

// 内容接口
export interface Content {
  id: UUID;
  title: string;
  description: string;
  body: string;
  platform: Platform;
  contentType: ContentType;
  status: ContentStatus;
  tags: string[];
  authorId: UUID;
  templateId?: UUID;
  
  // 发布相关
  publishedAt?: Timestamp;
  scheduledAt?: Timestamp;
  
  // 元数据
  metadata: ContentMetadata;
  
  // 分析数据
  analytics: ContentAnalytics;
  
  // 时间戳
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

// 内容元数据
export interface ContentMetadata {
  wordCount: number;
  readingTime: number; // 分钟
  seoScore?: number;
  sentiment?: 'positive' | 'neutral' | 'negative';
  targetAudience?: string[];
  keywords?: string[];
  hashtags?: string[];
}

// 内容分析数据
export interface ContentAnalytics {
  views: number;
  likes: number;
  shares: number;
  comments: number;
  saves: number;
  clicks: number;
  engagement: number; // 参与度百分比
  reach: number;
  impressions: number;
  
  // 转化数据
  conversions?: number;
  revenue?: number;
  
  // 时间序列数据
  dailyStats?: DailyStats[];
}

// 每日统计数据
export interface DailyStats {
  date: string; // YYYY-MM-DD
  views: number;
  likes: number;
  shares: number;
  comments: number;
  engagement: number;
}

// 内容模板
export interface ContentTemplate {
  id: UUID;
  name: string;
  description: string;
  platform: Platform;
  contentType: ContentType;
  
  // 模板内容
  template: string;
  variables: TemplateVariable[];
  
  // 模板配置
  config: TemplateConfig;
  
  // 使用统计
  usageCount: number;
  rating: number;
  
  // 创建信息
  createdBy: UUID;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

// 模板变量
export interface TemplateVariable {
  name: string;
  type: 'text' | 'number' | 'boolean' | 'select' | 'multiselect' | 'date';
  label: string;
  description?: string;
  required: boolean;
  defaultValue?: any;
  options?: string[]; // 用于select类型
  validation?: VariableValidation;
}

// 变量验证规则
export interface VariableValidation {
  minLength?: number;
  maxLength?: number;
  min?: number;
  max?: number;
  pattern?: string; // 正则表达式
  customValidator?: string; // 自定义验证函数名
}

// 模板配置
export interface TemplateConfig {
  allowCustomization: boolean;
  autoSave: boolean;
  versionControl: boolean;
  collaborationEnabled: boolean;
  aiAssistance: boolean;
}

// 内容计划
export interface ContentPlan {
  id: UUID;
  name: string;
  description: string;
  
  // 计划配置
  startDate: Timestamp;
  endDate: Timestamp;
  platforms: Platform[];
  contentTypes: ContentType[];
  
  // 目标设置
  goals: ContentGoal[];
  targetAudience: AudienceSegment[];
  
  // 内容列表
  contents: UUID[];
  
  // 状态
  status: 'planning' | 'active' | 'paused' | 'completed' | 'cancelled';
  
  // 创建信息
  createdBy: UUID;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

// 内容目标
export interface ContentGoal {
  type: 'views' | 'engagement' | 'conversions' | 'revenue' | 'followers';
  target: number;
  current: number;
  deadline?: Timestamp;
}

// 受众细分
export interface AudienceSegment {
  id: UUID;
  name: string;
  description: string;
  demographics: Demographics;
  interests: string[];
  behaviors: string[];
  platforms: Platform[];
}

// 人口统计信息
export interface Demographics {
  ageRange: [number, number];
  gender: 'male' | 'female' | 'all';
  location: string[];
  income?: [number, number];
  education?: string[];
  occupation?: string[];
}

// API响应类型
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: ApiError;
  message?: string;
  timestamp: Timestamp;
}

// API错误类型
export interface ApiError {
  code: string;
  message: string;
  details?: any;
  stack?: string;
}

// 分页参数
export interface PaginationParams {
  page: number;
  limit: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

// 分页响应
export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

// 搜索和筛选参数
export interface SearchParams {
  query?: string;
  platform?: Platform;
  status?: ContentStatus;
  contentType?: ContentType;
  authorId?: UUID;
  tags?: string[];
  dateRange?: {
    start: Timestamp;
    end: Timestamp;
  };
}

// 批量操作类型
export interface BatchOperation {
  action: 'publish' | 'archive' | 'delete' | 'update_status' | 'add_tags' | 'remove_tags';
  contentIds: UUID[];
  params?: any;
}

// 内容版本
export interface ContentVersion {
  id: UUID;
  contentId: UUID;
  versionNumber: number;
  title: string;
  body: string;
  changes: ContentChange[];
  createdBy: UUID;
  createdAt: Timestamp;
  comment?: string;
}

// 内容变更记录
export interface ContentChange {
  field: string;
  oldValue: any;
  newValue: any;
  operation: 'create' | 'update' | 'delete';
}

// 协作相关类型
export interface CollaborationSession {
  id: UUID;
  contentId: UUID;
  participants: UUID[];
  startedAt: Timestamp;
  endedAt?: Timestamp;
  changes: ContentChange[];
}

// 评论系统
export interface Comment {
  id: UUID;
  contentId: UUID;
  authorId: UUID;
  parentId?: UUID; // 用于回复
  text: string;
  resolved: boolean;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

// 通知类型
export interface Notification {
  id: UUID;
  userId: UUID;
  type: 'content_published' | 'comment_added' | 'collaboration_invite' | 'goal_achieved';
  title: string;
  message: string;
  data?: any;
  read: boolean;
  createdAt: Timestamp;
}

// 系统配置
export interface SystemConfig {
  aiProvider: 'openai' | 'anthropic' | 'google';
  defaultPlatform: Platform;
  autoSaveInterval: number; // 秒
  maxFileSize: number; // 字节
  allowedFileTypes: string[];
  features: {
    collaboration: boolean;
    aiAssistance: boolean;
    analytics: boolean;
    scheduling: boolean;
  };
}

// 导出所有类型的联合类型，便于类型检查
export type AllTypes = 
  | User 
  | Content 
  | ContentTemplate 
  | ContentPlan 
  | AudienceSegment 
  | ContentVersion 
  | Comment 
  | Notification 
  | SystemConfig;
